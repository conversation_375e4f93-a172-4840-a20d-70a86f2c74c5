/**
 * Утилиты для работы с авторизацией в middleware
 */

/**
 * Проверяет, авторизован ли пользователь через Astro.locals
 * @param locals Astro.locals объект
 * @returns boolean
 */
export function isUserAuthenticated(locals: App.Locals): boolean {
  return locals.isAuthenticated === true;
}

/**
 * Получает информацию о текущем пользователе из Astro.locals
 * @param locals Astro.locals объект
 * @returns user object или undefined
 */
export function getCurrentUser(locals: App.Locals): App.Locals['user'] {
  return locals.user;
}

/**
 * Проверяет, имеет ли пользователь права администратора
 * @param locals Astro.locals объект
 * @returns boolean
 */
export function isAdmin(locals: App.Locals): boolean {
  // В нашем случае, если пользователь авторизован через PocketBase,
  // он считается администратором
  return locals.isAuthenticated === true;
}