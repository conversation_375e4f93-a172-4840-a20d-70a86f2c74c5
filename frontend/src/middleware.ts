import { defineMiddleware } from 'astro:middleware';
import PocketBase from 'pocketbase';

// Получаем URL PocketBase из переменных окружения
const baseUrl = import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru';

/**
 * Проверяет валидность токена PocketBase
 * @param token JWT токен
 * @returns Promise<boolean>
 */
async function validateToken(token: string): Promise<{ isValid: boolean; user?: any }> {
  try {
    const pb = new PocketBase(baseUrl);

    // Устанавливаем токен в authStore
    pb.authStore.save(token);

    // Проверяем валидность токена
    if (!pb.authStore.isValid) {
      return { isValid: false };
    }

    // Сначала пытаемся проверить как суперпользователя (админа)
    try {
      const adminUser = await pb.admins.authRefresh();
      return {
        isValid: true,
        user: {
          id: adminUser.admin.id,
          email: adminUser.admin.email,
          token: pb.authStore.token,
          type: 'admin'
        }
      };
    } catch (adminError) {
      // Если не админ, пытаемся как обычного пользователя
      try {
        const user = await pb.collection('users').authRefresh();
        return {
          isValid: true,
          user: {
            id: user.record.id,
            email: user.record.email,
            token: pb.authStore.token,
            type: 'user'
          }
        };
      } catch (userError) {
        console.error('Ошибка при проверке токена пользователя:', userError);
        return { isValid: false };
      }
    }
  } catch (error) {
    console.error('Ошибка при валидации токена:', error);
    return { isValid: false };
  }
}

/**
 * Извлекает токен из cookies или заголовков
 * @param request Request объект
 * @returns string | null
 */
function extractToken(request: Request): string | null {
  // Проверяем заголовок Authorization
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Проверяем cookies
  const cookieHeader = request.headers.get('Cookie');
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return cookies['pb_token'] || null;
  }

  return null;
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { request, locals } = context;

  // Инициализируем locals с значениями по умолчанию
  locals.isAuthenticated = false;
  locals.user = undefined;

  // Извлекаем токен из запроса
  const token = extractToken(request);

  if (token) {
    // Проверяем валидность токена
    const validation = await validateToken(token);

    if (validation.isValid && validation.user) {
      locals.isAuthenticated = true;
      locals.user = validation.user;

      console.log('Пользователь авторизован:', validation.user.email);
    } else {
      console.log('Токен недействителен');
    }
  } else {
    console.log('Токен не найден в запросе');
  }

  // Продолжаем обработку запроса
  return next();
});
