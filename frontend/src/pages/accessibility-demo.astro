---
import Layout from '@/layouts/Layout.astro'
---

<Layout title="Демо версии для слабовидящих - STOM-LINE" description="Демонстрационная страница для тестирования версии сайта для слабовидящих">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8">
        Тестирование версии для слабовидящих
      </h1>

      <div class="bg-blue-50 border border-blue-200 p-6 mb-8 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">📋 Как тестировать:</h2>
        <ol class="list-decimal pl-6 space-y-3 text-lg">
          <li><strong>Найдите кнопку "Для слабовидящих"</strong> в правом верхнем углу страницы</li>
          <li><strong>Нажмите на неё</strong> - страница должна стать черно-белой с увеличенным шрифтом</li>
          <li><strong>Нажмите кнопку с шестеренкой ⚙️</strong> рядом с переключателем</li>
          <li><strong>Попробуйте разные настройки:</strong> размер шрифта и контрастность</li>
          <li><strong>Горячая клавиша:</strong> <kbd class="bg-gray-200 px-2 py-1 rounded font-mono">Alt + A</kbd></li>
        </ol>
      </div>

      <!-- Демонстрационный контент -->
      <div class="space-y-12">

        <!-- Заголовки -->
        <section class="bg-white p-6 rounded-lg border">
          <h2 class="text-2xl font-bold mb-6">Заголовки разных уровней</h2>
          <h1 class="text-3xl font-bold mb-3">Заголовок H1 (32px → 40px)</h1>
          <h2 class="text-2xl font-semibold mb-3">Заголовок H2 (28px → 36px)</h2>
          <h3 class="text-xl font-medium mb-3">Заголовок H3 (24px → 32px)</h3>
          <h4 class="text-lg font-medium mb-3">Заголовок H4 (20px → 28px)</h4>
        </section>

        <!-- Текст -->
        <section class="bg-white p-6 rounded-lg border">
          <h2 class="text-2xl font-bold mb-6">Обычный текст</h2>
          <p class="mb-4 text-lg">
            Это обычный абзац текста. В режиме для слабовидящих размер шрифта увеличивается
            с обычного до <strong>минимум 18px</strong>, а цвета становятся более контрастными.
          </p>
          <p class="mb-4 text-lg">
            <strong>Жирный текст</strong> и <em>курсивный текст</em> также хорошо видны.
          </p>
          <p class="text-lg">
            Все цвета меняются на <strong>черный текст на белом фоне</strong> для максимальной читаемости.
          </p>
        </section>

        <!-- Кнопки -->
        <section class="bg-white p-6 rounded-lg border">
          <h2 class="text-2xl font-bold mb-6">Кнопки и интерактивные элементы</h2>
          <p class="mb-4 text-lg">Все кнопки становятся больше (минимум 48x48px) с четкими границами:</p>
          <div class="space-y-4">
            <div class="flex flex-wrap gap-4">
              <button class="bg-blue-600 text-white px-6 py-3 rounded">
                Обычная кнопка
              </button>
              <button class="border-2 border-blue-600 text-blue-600 px-6 py-3 rounded">
                Кнопка с границей
              </button>
              <button class="bg-green-600 text-white px-6 py-3 rounded">
                Зеленая кнопка
              </button>
            </div>
          </div>
        </section>

        <!-- Ссылки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Ссылки</h2>
          <p class="mb-4">
            Вот <a href="/services" class="text-olive-600 hover:text-olive-800 underline">ссылка на услуги</a> и 
            <a href="/specialists" class="text-olive-600 hover:text-olive-800 underline">ссылка на специалистов</a>.
          </p>
        </section>

        <!-- Формы -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Элементы форм</h2>
          <form class="space-y-4 max-w-md">
            <div>
              <label for="name" class="block text-sm font-medium mb-2">Имя</label>
              <input 
                type="text" 
                id="name" 
                name="name" 
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-olive-500"
                placeholder="Введите ваше имя"
              />
            </div>
            <div>
              <label for="email" class="block text-sm font-medium mb-2">Email</label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-olive-500"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label for="message" class="block text-sm font-medium mb-2">Сообщение</label>
              <textarea 
                id="message" 
                name="message" 
                rows="4"
                class="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-olive-500"
                placeholder="Введите ваше сообщение"
              ></textarea>
            </div>
            <button type="submit" class="bg-olive-600 text-white px-6 py-3 rounded hover:bg-olive-700">
              Отправить
            </button>
          </form>
        </section>

        <!-- Карточки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Карточки</h2>
          <div class="grid md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow-md border">
              <h3 class="text-xl font-semibold mb-3">Карточка 1</h3>
              <p class="text-gray-600 mb-4">
                Описание первой карточки с некоторым текстом для демонстрации.
              </p>
              <button class="bg-olive-600 text-white px-4 py-2 rounded hover:bg-olive-700">
                Подробнее
              </button>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md border">
              <h3 class="text-xl font-semibold mb-3">Карточка 2</h3>
              <p class="text-gray-600 mb-4">
                Описание второй карточки с некоторым текстом для демонстрации.
              </p>
              <button class="bg-olive-600 text-white px-4 py-2 rounded hover:bg-olive-700">
                Подробнее
              </button>
            </div>
          </div>
        </section>

        <!-- Списки -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Списки</h2>
          <div class="grid md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium mb-3">Маркированный список</h3>
              <ul class="list-disc pl-6 space-y-2">
                <li>Первый пункт списка</li>
                <li>Второй пункт списка</li>
                <li>Третий пункт списка</li>
                <li>Четвертый пункт списка</li>
              </ul>
            </div>
            <div>
              <h3 class="text-lg font-medium mb-3">Нумерованный список</h3>
              <ol class="list-decimal pl-6 space-y-2">
                <li>Первый шаг</li>
                <li>Второй шаг</li>
                <li>Третий шаг</li>
                <li>Четвертый шаг</li>
              </ol>
            </div>
          </div>
        </section>

        <!-- Навигация с клавиатуры -->
        <section>
          <h2 class="text-2xl font-semibold mb-4">Тестирование навигации</h2>
          <p class="mb-4">
            Используйте клавишу <kbd class="bg-gray-200 px-2 py-1 rounded">Tab</kbd> для навигации по элементам:
          </p>
          <div class="space-x-4">
            <button class="bg-blue-600 text-white px-4 py-2 rounded">Кнопка 1</button>
            <button class="bg-green-600 text-white px-4 py-2 rounded">Кнопка 2</button>
            <button class="bg-purple-600 text-white px-4 py-2 rounded">Кнопка 3</button>
          </div>
        </section>
      </div>

      <!-- Результаты тестирования -->
      <div class="mt-12 bg-green-50 border border-green-200 p-6 rounded-lg">
        <h2 class="text-2xl font-bold mb-6">✅ Что должно произойти в режиме доступности:</h2>
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-semibold mb-3">Визуальные изменения:</h3>
            <ul class="space-y-2 text-lg">
              <li>📝 Размер шрифта: 18px → 20px → 24px</li>
              <li>🎨 Цвета: черный текст на белом фоне</li>
              <li>🔲 Четкие черные границы у всех элементов</li>
              <li>🚫 Убраны анимации и эффекты</li>
              <li>📏 Кнопки увеличены до 48x48px минимум</li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-3">Функциональные изменения:</h3>
            <ul class="space-y-2 text-lg">
              <li>⌨️ Улучшенная навигация с клавиатуры</li>
              <li>🔍 Четкие фокусы при Tab-навигации</li>
              <li>💾 Настройки сохраняются в браузере</li>
              <li>🎯 Горячие клавиши работают</li>
              <li>📱 Адаптивность сохраняется</li>
            </ul>
          </div>
        </div>

        <div class="mt-6 p-4 bg-white rounded border-l-4 border-green-500">
          <p class="text-lg font-semibold">🎯 Главное:</p>
          <p class="text-lg">
            Версия для слабовидящих должна <strong>сохранить функциональность</strong> сайта,
            но сделать его <strong>более читаемым и доступным</strong> для людей с нарушениями зрения.
          </p>
        </div>
      </div>
    </div>
  </div>
</Layout>

<style>
  kbd {
    font-family: monospace;
    font-size: 0.875em;
  }
</style>
