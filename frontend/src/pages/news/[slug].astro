---
import Layout from '../../layouts/Layout.astro';
import PocketBase from 'pocketbase';
import { Button } from '../../components/ui/button';
import { CalendarIcon, ArrowLeftIcon, ShareIcon } from 'lucide-react';
import EditButtonServer from '@/components/admin/EditButtonServer.astro';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

export async function getStaticPaths() {
  const pb = new PocketBase(PUBLIC_API_URL);

  try {
    const news = await pb.collection('news').getFullList();

    return news.map((newsItem) => ({
      params: { slug: newsItem.slug || newsItem.id },
      props: { newsItem },
    }));
  } catch (error) {
    console.error('Ошибка при получении данных о новостях:', error);
    return [];
  }
}

const { newsItem } = Astro.props;

// Функция для получения URL изображения
const getImageUrl = (newsItem: any) => {
  if (!newsItem.image) return '/placeholder-news.jpg';
  return `${PUBLIC_API_URL}/api/files/news/${newsItem.id}/${newsItem.image}`;
};

// Функция для форматирования даты
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
---

<Layout 
  title={newsItem.meta_title || newsItem.title}
  description={newsItem.meta_description || `Новость: ${newsItem.title}`}
>
  <div class="min-h-screen bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
    <!-- Background elements -->
    <div class="pointer-events-none absolute inset-0 z-0 overflow-hidden">
      <div class="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]"></div>
      <div class="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]"></div>
      <div class="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]"></div>
    </div>

    <!-- Grid lines -->
    <div class="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]"></div>

    <div class="relative z-10 container mx-auto px-4 py-16">
      <!-- Navigation -->
      <div class="mb-8">
        <Button variant="outline" asChild class="border-olive-200 text-olive-700 hover:bg-olive-50">
          <a href="/news">
            <ArrowLeftIcon class="mr-2 h-4 w-4" />
            Вернуться к новостям
          </a>
        </Button>
      </div>

      <!-- Article -->
      <article class="max-w-4xl mx-auto">
        <!-- Header -->
        <header class="mb-8">
          <div class="flex items-center gap-2 text-sm text-gray-500 mb-4">
            <CalendarIcon class="h-4 w-4" />
            {formatDate(newsItem.date)}
          </div>
          
          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            {newsItem.title}
          </h1>

          <!-- Edit Button -->
          <div class="mb-6">
            <EditButtonServer collection="news" id={newsItem.id} />
          </div>
        </header>

        <!-- Featured Image -->
        {newsItem.image && (
          <div class="mb-8 rounded-2xl overflow-hidden shadow-2xl">
            <img
              src={getImageUrl(newsItem)}
              alt={newsItem.title}
              class="w-full h-64 md:h-96 object-cover"
            />
          </div>
        )}

        <!-- Content -->
        <div class="prose prose-lg max-w-none">
          <div 
            class="text-gray-700 leading-relaxed"
            set:html={newsItem.content}
          />
        </div>

        <!-- Share Section -->
        <div class="mt-12 pt-8 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <span class="text-sm font-medium text-gray-500">Поделиться:</span>
              <div class="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  class="border-olive-200 text-olive-700 hover:bg-olive-50"
                  onclick={`navigator.share ? navigator.share({title: '${newsItem.title}', url: window.location.href}) : navigator.clipboard.writeText(window.location.href)`}
                >
                  <ShareIcon class="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div class="text-sm text-gray-500">
              Опубликовано: {formatDate(newsItem.created)}
            </div>
          </div>
        </div>

        <!-- Related News -->
        <div class="mt-16">
          <h3 class="text-2xl font-bold text-gray-900 mb-8">Другие новости</h3>
          <div class="grid md:grid-cols-2 gap-6">
            <!-- Здесь можно добавить связанные новости -->
          </div>
        </div>
      </article>

      <!-- Call to Action -->
      <div class="mt-16 text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-olive-100">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">
            Хотите быть в курсе всех новостей?
          </h3>
          <p class="text-gray-600 mb-6">
            Следите за нашими обновлениями и не пропускайте важные события клиники
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild class="bg-olive-500 hover:bg-olive-600 text-white">
              <a href="/news">Все новости</a>
            </Button>
            <Button asChild variant="outline" class="border-olive-200 text-olive-700 hover:bg-olive-50">
              <a href="/contact">Связаться с нами</a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<style>
  .prose {
    @apply text-gray-700;
  }
  
  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    @apply text-gray-900 font-bold;
  }
  
  .prose h2 {
    @apply text-2xl mt-8 mb-4;
  }
  
  .prose h3 {
    @apply text-xl mt-6 mb-3;
  }
  
  .prose p {
    @apply mb-4 leading-relaxed;
  }
  
  .prose ul,
  .prose ol {
    @apply mb-4 pl-6;
  }
  
  .prose li {
    @apply mb-2;
  }
  
  .prose a {
    @apply text-olive-600 hover:text-olive-700 underline;
  }
  
  .prose blockquote {
    @apply border-l-4 border-olive-300 pl-4 italic text-gray-600 my-6;
  }
  
  .prose img {
    @apply rounded-lg shadow-md my-6;
  }
</style>
