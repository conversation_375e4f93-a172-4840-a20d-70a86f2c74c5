---
import Layout from '@/layouts/Layout.astro'
---

<Layout title="Тест всех компонентов - Версия для слабовидящих" description="Тестирование всех компонентов сайта в версии для слабовидящих">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-4xl font-bold text-center mb-8">
        Тест всех компонентов в версии для слабовидящих
      </h1>
      
      <div class="bg-yellow-50 border border-yellow-200 p-6 mb-8 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">🧪 Инструкция по тестированию:</h2>
        <ol class="list-decimal pl-6 space-y-2 text-lg">
          <li><strong>Включите версию для слабовидящих</strong> - кнопка в правом верхнем углу</li>
          <li><strong>Прокрутите страницу</strong> и проверьте все компоненты</li>
          <li><strong>Попробуйте разные настройки</strong> размера шрифта и контрастности</li>
          <li><strong>Проверьте навигацию</strong> с клавиатуры (Tab)</li>
        </ol>
      </div>

      <!-- Тест заголовков -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Заголовки</h2>
        <h1 class="text-4xl font-bold mb-3">H1 - Главный заголовок</h1>
        <h2 class="text-3xl font-semibold mb-3">H2 - Заголовок раздела</h2>
        <h3 class="text-2xl font-medium mb-3">H3 - Подзаголовок</h3>
        <h4 class="text-xl font-medium mb-3">H4 - Заголовок подраздела</h4>
        <h5 class="text-lg font-medium mb-3">H5 - Мелкий заголовок</h5>
        <h6 class="text-base font-medium mb-3">H6 - Самый мелкий заголовок</h6>
      </section>

      <!-- Тест текста -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Текстовые элементы</h2>
        <p class="mb-4 text-lg">
          Это обычный абзац текста. В версии для слабовидящих он должен быть хорошо читаемым 
          с увеличенным размером шрифта и высоким контрастом.
        </p>
        <p class="mb-4">
          <strong>Жирный текст</strong>, <em>курсивный текст</em>, 
          <u>подчеркнутый текст</u> и <mark>выделенный текст</mark>.
        </p>
        <blockquote class="border-l-4 border-gray-300 pl-4 italic mb-4">
          "Это цитата, которая должна быть хорошо видна в версии для слабовидящих."
        </blockquote>
      </section>

      <!-- Тест кнопок -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Кнопки и ссылки</h2>
        <div class="space-y-4">
          <div class="flex flex-wrap gap-4">
            <button class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700">
              Основная кнопка
            </button>
            <button class="border-2 border-blue-600 text-blue-600 px-6 py-3 rounded hover:bg-blue-50">
              Вторичная кнопка
            </button>
            <button class="bg-green-600 text-white px-6 py-3 rounded hover:bg-green-700">
              Успех
            </button>
            <button class="bg-red-600 text-white px-6 py-3 rounded hover:bg-red-700">
              Опасность
            </button>
          </div>
          <div class="space-y-2">
            <p>Ссылки: <a href="#" class="text-blue-600 hover:text-blue-800 underline">Обычная ссылка</a></p>
            <p>Посещенная: <a href="#" class="text-purple-600 hover:text-purple-800 underline">Посещенная ссылка</a></p>
          </div>
        </div>
      </section>

      <!-- Тест форм -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Формы</h2>
        <form class="space-y-6 max-w-md">
          <div>
            <label for="name" class="block text-lg font-medium mb-2">Имя *</label>
            <input 
              type="text" 
              id="name" 
              name="name" 
              required
              class="w-full px-4 py-3 border-2 border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Введите ваше имя"
            />
          </div>
          
          <div>
            <label for="email" class="block text-lg font-medium mb-2">Email *</label>
            <input 
              type="email" 
              id="email" 
              name="email" 
              required
              class="w-full px-4 py-3 border-2 border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label for="phone" class="block text-lg font-medium mb-2">Телефон</label>
            <input 
              type="tel" 
              id="phone" 
              name="phone" 
              class="w-full px-4 py-3 border-2 border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="+7 (999) 123-45-67"
            />
          </div>

          <div>
            <label for="service" class="block text-lg font-medium mb-2">Услуга</label>
            <select 
              id="service" 
              name="service"
              class="w-full px-4 py-3 border-2 border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Выберите услугу</option>
              <option value="consultation">Консультация</option>
              <option value="cleaning">Чистка зубов</option>
              <option value="filling">Пломбирование</option>
              <option value="whitening">Отбеливание</option>
            </select>
          </div>

          <div>
            <label for="message" class="block text-lg font-medium mb-2">Сообщение</label>
            <textarea 
              id="message" 
              name="message" 
              rows="4"
              class="w-full px-4 py-3 border-2 border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Опишите ваш вопрос или пожелания"
            ></textarea>
          </div>

          <div class="flex items-center">
            <input 
              type="checkbox" 
              id="agree" 
              name="agree" 
              required
              class="h-5 w-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500"
            />
            <label for="agree" class="ml-3 text-lg">
              Я согласен с <a href="#" class="text-blue-600 underline">политикой конфиденциальности</a> *
            </label>
          </div>

          <button 
            type="submit" 
            class="w-full bg-blue-600 text-white px-6 py-4 rounded text-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Отправить заявку
          </button>
        </form>
      </section>

      <!-- Тест карточек -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Карточки</h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="bg-white border-2 border-gray-200 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <span class="text-2xl">🦷</span>
            </div>
            <h3 class="text-xl font-semibold mb-3">Лечение кариеса</h3>
            <p class="text-gray-600 mb-4">
              Современные методы лечения кариеса с использованием качественных материалов.
            </p>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold">от 3 000 ₽</span>
              <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                Записаться
              </button>
            </div>
          </div>

          <div class="bg-white border-2 border-gray-200 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <span class="text-2xl">✨</span>
            </div>
            <h3 class="text-xl font-semibold mb-3">Чистка зубов</h3>
            <p class="text-gray-600 mb-4">
              Профессиональная гигиена полости рта для здоровья ваших зубов.
            </p>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold">от 2 500 ₽</span>
              <button class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                Записаться
              </button>
            </div>
          </div>

          <div class="bg-white border-2 border-gray-200 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
            <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <span class="text-2xl">👨‍⚕️</span>
            </div>
            <h3 class="text-xl font-semibold mb-3">Консультация</h3>
            <p class="text-gray-600 mb-4">
              Первичная консультация специалиста с составлением плана лечения.
            </p>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold">от 1 000 ₽</span>
              <button class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                Записаться
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Тест списков -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Списки</h2>
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold mb-4">Маркированный список</h3>
            <ul class="list-disc pl-6 space-y-2 text-lg">
              <li>Первый пункт списка</li>
              <li>Второй пункт списка</li>
              <li>Третий пункт с <strong>важной информацией</strong></li>
              <li>Четвертый пункт списка</li>
            </ul>
          </div>
          <div>
            <h3 class="text-xl font-semibold mb-4">Нумерованный список</h3>
            <ol class="list-decimal pl-6 space-y-2 text-lg">
              <li>Первый шаг процедуры</li>
              <li>Второй шаг процедуры</li>
              <li>Третий шаг с <em>дополнительными деталями</em></li>
              <li>Финальный шаг</li>
            </ol>
          </div>
        </div>
      </section>

      <!-- Тест таблицы -->
      <section class="mb-12 bg-white p-6 rounded-lg border">
        <h2 class="text-2xl font-bold mb-6">Таблица цен</h2>
        <div class="overflow-x-auto">
          <table class="w-full border-collapse border-2 border-gray-300">
            <thead>
              <tr class="bg-gray-100">
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Услуга</th>
                <th class="border border-gray-300 px-4 py-3 text-left font-semibold">Описание</th>
                <th class="border border-gray-300 px-4 py-3 text-right font-semibold">Цена</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="border border-gray-300 px-4 py-3">Консультация</td>
                <td class="border border-gray-300 px-4 py-3">Первичный осмотр и консультация</td>
                <td class="border border-gray-300 px-4 py-3 text-right">1 000 ₽</td>
              </tr>
              <tr class="bg-gray-50">
                <td class="border border-gray-300 px-4 py-3">Лечение кариеса</td>
                <td class="border border-gray-300 px-4 py-3">Лечение с постановкой пломбы</td>
                <td class="border border-gray-300 px-4 py-3 text-right">от 3 000 ₽</td>
              </tr>
              <tr>
                <td class="border border-gray-300 px-4 py-3">Чистка зубов</td>
                <td class="border border-gray-300 px-4 py-3">Профессиональная гигиена</td>
                <td class="border border-gray-300 px-4 py-3 text-right">2 500 ₽</td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <!-- Результаты тестирования -->
      <section class="bg-green-50 border border-green-200 p-6 rounded-lg">
        <h2 class="text-2xl font-bold mb-6">✅ Чек-лист для проверки</h2>
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-semibold mb-3">Визуальные изменения:</h3>
            <ul class="space-y-2">
              <li>□ Размер шрифта увеличился до 18px+</li>
              <li>□ Все цвета стали черно-белыми</li>
              <li>□ Появились четкие границы у элементов</li>
              <li>□ Убрались анимации и эффекты</li>
              <li>□ Кнопки стали больше (48x48px+)</li>
            </ul>
          </div>
          <div>
            <h3 class="text-lg font-semibold mb-3">Функциональность:</h3>
            <ul class="space-y-2">
              <li>□ Навигация Tab работает корректно</li>
              <li>□ Фокусы хорошо видны</li>
              <li>□ Формы заполняются нормально</li>
              <li>□ Ссылки и кнопки кликабельны</li>
              <li>□ Настройки сохраняются</li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  </div>
</Layout>

<style>
  /* Дополнительные стили для демонстрации */
  .transition-shadow {
    transition: box-shadow 0.3s ease;
  }
</style>
