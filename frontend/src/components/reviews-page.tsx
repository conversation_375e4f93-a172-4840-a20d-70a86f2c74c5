import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  StarIcon,
  UserIcon,
  CalendarIcon,
  SearchIcon,
  FilterIcon,
  QuoteIcon,
  ThumbsUpIcon,
  HeartIcon,
  TrendingUpIcon,
  AwardIcon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { EditButton } from '@/components/admin/EditButton';
import { type Review } from '@/lib/api';

interface ReviewsPageProps {
  reviews: Review[];
}

interface ReviewCardProps {
  review: Review;
  index: number;
}

interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  recommendationRate: number;
}



const ReviewCard = ({ review, index }: ReviewCardProps) => {
  const [isLiked, setIsLiked] = useState(false);

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getImageUrl = (review: Review) => {
    if (!review.image) return null;
    return `https://pb.stom-line.ru/api/files/reviews/${review.id}/${review.image}`;
  };

  const getRatingStars = (rating: number = 5) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`h-4 w-4 ${
          i < rating
            ? 'fill-yellow-400 text-yellow-400'
            : 'fill-gray-200 text-gray-200'
        }`}
      />
    ));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ y: -5, scale: 1.02 }}
      className="group"
    >
      <Card className="h-full bg-gradient-to-br from-white via-white to-olive-50/30 backdrop-blur-sm border-olive-200/50 shadow-lg hover:shadow-xl transition-all duration-500 group-hover:border-olive-300 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-olive-100/50 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        <div className="absolute -bottom-2 -left-2 w-16 h-16 bg-gradient-to-tr from-olive-200/30 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        <CardHeader className="relative pb-4">
          <EditButton
            collection="reviews"
            id={review.id}
            position="top-right"
            variant="text"
            size="sm"
            className="bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors"
          />

          {/* Quote icon */}
          <div className="absolute top-4 left-4 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
            <QuoteIcon className="h-8 w-8 text-olive-500" />
          </div>

          <div className="flex items-start gap-4 relative z-10">
            {getImageUrl(review) ? (
              <div className="relative">
                <img
                  src={getImageUrl(review)!}
                  alt={review.author}
                  className="w-16 h-16 rounded-full object-cover border-3 border-olive-200 shadow-md group-hover:border-olive-300 transition-colors duration-300"
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-olive-500 rounded-full flex items-center justify-center border-2 border-white">
                  <AwardIcon className="h-3 w-3 text-white" />
                </div>
              </div>
            ) : (
              <div className="relative">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-olive-100 to-olive-200 flex items-center justify-center border-3 border-olive-200 shadow-md group-hover:border-olive-300 transition-colors duration-300">
                  <UserIcon className="h-8 w-8 text-olive-600" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-olive-500 rounded-full flex items-center justify-center border-2 border-white">
                  <AwardIcon className="h-3 w-3 text-white" />
                </div>
              </div>
            )}

            <div className="flex-1">
              <h3 className="text-lg font-bold text-gray-900 group-hover:text-olive-700 transition-colors duration-300">
                {review.author}
              </h3>

              {review.date && (
                <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                  <CalendarIcon className="h-4 w-4" />
                  {formatDate(review.date)}
                </div>
              )}

              {/* Rating stars */}
              <div className="flex items-center gap-1 mt-3">
                {getRatingStars(5)}
                <span className="ml-2 text-sm font-medium text-olive-600">5.0</span>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {review.title && (
            <h4 className="text-lg font-semibold text-gray-900 mb-4 group-hover:text-olive-700 transition-colors duration-300">
              {review.title}
            </h4>
          )}

          {review.content && (
            <div
              className="prose prose-olive max-w-none text-gray-700 leading-relaxed mb-4 prose-p:mb-2 prose-strong:text-olive-700"
              dangerouslySetInnerHTML={{ __html: review.content }}
            />
          )}

          {/* Action buttons */}
          <div className="flex items-center justify-between pt-4 border-t border-olive-100">
            <div className="flex items-center gap-3">
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsLiked(!isLiked)}
                className={`flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 ${
                  isLiked
                    ? 'bg-olive-100 text-olive-700 border border-olive-200'
                    : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-olive-50 hover:text-olive-600'
                }`}
              >
                <HeartIcon className={`h-4 w-4 ${isLiked ? 'fill-olive-500 text-olive-500' : ''}`} />
                Полезно
              </motion.button>

              <Badge variant="secondary" className="bg-olive-50 text-olive-700 border-olive-200">
                Проверенный отзыв
              </Badge>
            </div>

            <div className="flex items-center gap-1 text-xs text-gray-400">
              <TrendingUpIcon className="h-3 w-3" />
              <span>Рекомендует</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const ReviewsPage = ({ reviews }: ReviewsPageProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [visibleReviews, setVisibleReviews] = useState(9);
  const [filterRating, setFilterRating] = useState<number | null>(null);
  const [sortBy, setSortBy] = useState<'date' | 'rating' | 'helpful'>('date');
  const [isLoading, setIsLoading] = useState(false);

  // Фильтрация и сортировка отзывов
  const filteredReviews = reviews
    .filter(review => {
      const matchesSearch = review.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (review.title && review.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
                           (review.content && review.content.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesRating = filterRating === null || true; // Предполагаем, что все отзывы имеют рейтинг 5

      return matchesSearch && matchesRating && review.is_published !== false;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.date || '').getTime() - new Date(a.date || '').getTime();
        case 'rating':
          return 5 - 5; // Все отзывы имеют рейтинг 5
        case 'helpful':
          return Math.random() - 0.5; // Случайная сортировка для демонстрации
        default:
          return 0;
      }
    });

  const loadMore = () => {
    setIsLoading(true);
    setTimeout(() => {
      setVisibleReviews(prev => prev + 9);
      setIsLoading(false);
    }, 500);
  };

  // Статистика отзывов
  const stats: ReviewStats = {
    totalReviews: filteredReviews.length,
    averageRating: 5.0,
    recommendationRate: 100
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Enhanced Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <motion.div
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -left-[10%] top-[20%] h-[400px] w-[400px] rounded-full bg-gradient-to-r from-olive-300/20 to-olive-400/30 blur-[120px]"
        />
        <motion.div
          animate={{
            x: [0, -25, 0],
            y: [0, 15, 0],
            scale: [1, 0.9, 1]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -right-[5%] top-[40%] h-[300px] w-[300px] rounded-full bg-gradient-to-l from-olive-400/25 to-olive-500/20 blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, 20, 0],
            y: [0, -30, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 10
          }}
          className="absolute bottom-[20%] left-[20%] h-[350px] w-[350px] rounded-full bg-gradient-to-tr from-olive-300/15 to-olive-200/25 blur-[140px]"
        />
      </div>

      {/* Animated grid pattern */}
      <div className="pointer-events-none absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.03] animate-pulse" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-20">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Badge className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white mb-8 px-6 py-2 text-sm font-medium shadow-lg">
              <AwardIcon className="h-4 w-4 mr-2" />
              Отзывы пациентов
            </Badge>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-8"
          >
            Отзывы наших пациентов
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto mb-12 leading-relaxed"
          >
            Узнайте, что говорят о нас наши пациенты. Их мнение — наша лучшая рекомендация и источник вдохновения для постоянного совершенствования
          </motion.p>

          {/* Enhanced Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="max-w-2xl mx-auto"
          >
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="relative flex-1 w-full">
                <SearchIcon className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Поиск по отзывам, именам, услугам..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 h-14 text-lg border-olive-200 focus:border-olive-400 bg-white/80 backdrop-blur-sm shadow-lg rounded-xl"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="lg"
                  className="border-olive-200 text-olive-700 hover:bg-olive-50 bg-white/80 backdrop-blur-sm shadow-lg"
                  onClick={() => setFilterRating(filterRating === 5 ? null : 5)}
                >
                  <StarIcon className="h-4 w-4 mr-2 fill-yellow-400 text-yellow-400" />
                  5★
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  className="border-olive-200 text-olive-700 hover:bg-olive-50 bg-white/80 backdrop-blur-sm shadow-lg"
                  onClick={() => setSortBy(sortBy === 'date' ? 'helpful' : 'date')}
                >
                  <FilterIcon className="h-4 w-4 mr-2" />
                  {sortBy === 'date' ? 'По дате' : 'По полезности'}
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Enhanced Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20"
        >
          <motion.div
            whileHover={{ scale: 1.05, y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
            className="text-center bg-gradient-to-br from-white via-white to-olive-50/30 backdrop-blur-sm rounded-2xl p-8 border border-olive-200/50 shadow-xl hover:shadow-2xl transition-all duration-500 group"
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-olive-400/10 to-olive-500/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative text-4xl md:text-5xl font-bold bg-gradient-to-r from-olive-600 to-olive-700 bg-clip-text text-transparent mb-3">
                {stats.totalReviews}
              </div>
            </div>
            <div className="text-gray-600 font-medium">Отзывов пациентов</div>
            <div className="mt-2 text-sm text-olive-600 font-medium">
              <TrendingUpIcon className="h-4 w-4 inline mr-1" />
              +12 за месяц
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05, y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
            className="text-center bg-gradient-to-br from-white via-white to-olive-50/30 backdrop-blur-sm rounded-2xl p-8 border border-olive-200/50 shadow-xl hover:shadow-2xl transition-all duration-500 group"
          >
            <div className="flex items-center justify-center gap-2 mb-3">
              <span className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-olive-600 to-olive-700 bg-clip-text text-transparent">
                {stats.averageRating}
              </span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <motion.div
                    key={star}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: star * 0.1 }}
                  >
                    <StarIcon className="h-6 w-6 fill-yellow-400 text-yellow-400" />
                  </motion.div>
                ))}
              </div>
            </div>
            <div className="text-gray-600 font-medium">Средняя оценка</div>
            <div className="mt-2 text-sm text-olive-600 font-medium">
              Максимальный рейтинг
            </div>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05, y: -5 }}
            transition={{ type: "spring", stiffness: 300 }}
            className="text-center bg-gradient-to-br from-white via-white to-olive-50/30 backdrop-blur-sm rounded-2xl p-8 border border-olive-200/50 shadow-xl hover:shadow-2xl transition-all duration-500 group"
          >
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-olive-400/10 to-olive-500/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className="relative text-4xl md:text-5xl font-bold bg-gradient-to-r from-olive-600 to-olive-700 bg-clip-text text-transparent mb-3">
                {stats.recommendationRate}%
              </div>
            </div>
            <div className="text-gray-600 font-medium">Рекомендуют нас</div>
            <div className="mt-2 text-sm text-olive-600 font-medium">
              <ThumbsUpIcon className="h-4 w-4 inline mr-1" />
              Довольны результатом
            </div>
          </motion.div>
        </motion.div>

        {/* Enhanced Reviews Grid */}
        {filteredReviews.length > 0 ? (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
            >
              <AnimatePresence mode="popLayout">
                {filteredReviews.slice(0, visibleReviews).map((review, index) => (
                  <ReviewCard key={review.id} review={review} index={index} />
                ))}
              </AnimatePresence>
            </motion.div>

            {/* Enhanced Load More Button */}
            {visibleReviews < filteredReviews.length && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center"
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={loadMore}
                    disabled={isLoading}
                    size="lg"
                    className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white px-12 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl"
                  >
                    {isLoading ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="h-5 w-5 border-2 border-white border-t-transparent rounded-full mr-2"
                      />
                    ) : (
                      <TrendingUpIcon className="h-5 w-5 mr-2" />
                    )}
                    {isLoading ? 'Загружаем...' : `Показать ещё отзывы (${filteredReviews.length - visibleReviews})`}
                  </Button>
                </motion.div>

                <p className="text-sm text-gray-500 mt-4">
                  Показано {Math.min(visibleReviews, filteredReviews.length)} из {filteredReviews.length} отзывов
                </p>
              </motion.div>
            )}
          </>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center py-20"
          >
            <div className="max-w-lg mx-auto">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, type: "spring", stiffness: 200 }}
                className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-olive-100 to-olive-200 rounded-full flex items-center justify-center shadow-lg"
              >
                <UserIcon className="h-16 w-16 text-olive-600" />
              </motion.div>

              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-3xl font-bold text-gray-900 mb-6"
              >
                {searchTerm ? 'Отзывы не найдены' : 'Отзывов пока нет'}
              </motion.h3>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-xl text-gray-600 mb-8 leading-relaxed"
              >
                {searchTerm
                  ? 'Попробуйте изменить поисковый запрос или воспользуйтесь фильтрами'
                  : 'Станьте первым, кто поделится своим опытом лечения в нашей клинике'
                }
              </motion.p>

              {searchTerm && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => setSearchTerm('')}
                    className="border-olive-200 text-olive-700 hover:bg-olive-50 shadow-lg"
                  >
                    <SearchIcon className="h-4 w-4 mr-2" />
                    Очистить поиск
                  </Button>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}

        {/* Enhanced CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          className="mt-20"
        >
          <div className="relative bg-gradient-to-br from-white via-olive-50/30 to-white backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-olive-200/50 text-center max-w-4xl mx-auto overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-olive-200/30 to-transparent rounded-full -translate-x-20 -translate-y-20" />
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-olive-300/20 to-transparent rounded-full translate-x-16 translate-y-16" />

            <div className="relative z-10">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, delay: 0.9 }}
                className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-olive-500 to-olive-600 rounded-full flex items-center justify-center shadow-xl"
              >
                <HeartIcon className="h-10 w-10 text-white" />
              </motion.div>

              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1 }}
                className="text-3xl md:text-4xl font-bold text-gray-900 mb-6"
              >
                Хотите поделиться своим опытом?
              </motion.h3>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.1 }}
                className="text-xl text-gray-600 mb-10 max-w-2xl mx-auto leading-relaxed"
              >
                Ваш отзыв поможет другим пациентам сделать правильный выбор и вдохновит нас на дальнейшее совершенствование качества услуг
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.2 }}
                className="flex flex-col sm:flex-row gap-6 justify-center"
              >
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 rounded-xl"
                  >
                    <HeartIcon className="h-5 w-5 mr-2" />
                    Оставить отзыв
                  </Button>
                </motion.div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-olive-200 text-olive-700 hover:bg-olive-50 bg-white/80 backdrop-blur-sm shadow-lg px-8 py-4 text-lg font-semibold rounded-xl"
                  >
                    <CalendarIcon className="h-5 w-5 mr-2" />
                    Записаться на прием
                  </Button>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
