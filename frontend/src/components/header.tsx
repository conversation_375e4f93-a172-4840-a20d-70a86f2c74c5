import { useEffect } from 'react'
import { <PERSON>u, Phone, Search as SearchIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from '@/components/ui/drawer'
import { Search } from '@/components/search'
import { AccessibilityToggle } from '@/components/accessibility/AccessibilityToggle'

interface NavItem {
  title: string
  href: string
}

export default function Header() {
  // Состояние для отслеживания скролла страницы
  // const [scrolled, setScrolled] = useState(true)

  const navItems: NavItem[] = [
    { title: 'Главная', href: '/' },
    { title: 'Услуги', href: '/services' },
    { title: 'Специалисты', href: '/specialists' },
    { title: 'Акции', href: '/promos' },
    { title: 'Новости', href: '/news' },
    { title: 'Отзывы', href: '/reviews' },
    { title: 'FAQ', href: '/faq' },
    { title: 'О нас', href: '/about' },
    { title: 'Контакты', href: '/contacts' }
  ]

  useEffect(() => {
    // Функционал скролла отключен, так как не используется в текущей версии
    // const handleScroll = () => {
    //   setScrolled(window.scrollY > 20)
    // }
    // window.addEventListener('scroll', handleScroll)
    // return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      {/* Пометка о том, что сайт в разработке */}
      <div className='bg-rose-400 text-white text-center font-bold py-1 sm:py-2 text-xs sm:text-sm sticky top-0 z-50'>
        Сайт в разработке. Демонстрационный контент.
      </div>
      <header
        // className={`sticky top-0 z-50 w-full transition-all duration-300 ${
        //   scrolled ? 'bg-olive-500/20 border-olive-200/50 border-b backdrop-blur-md' : 'bg-transparent'
        // }`}
        className='sticky top-[40px] z-40 w-full transition-all duration-300 bg-gradient-to-r from-olive-100/30 via-white/40 to-olive-100/30 backdrop-blur-md border-b border-olive-200/30'
      >
        <div className='container flex h-20 items-center justify-between px-4 md:px-6'>
          <a href='/' className='flex items-center gap-2'>
            <img src='/17a70891-5433-419e-99aa-33350107e7c9-removebg-preview.png' alt='Stom-Line' className='h-16 sm:h-20 md:h-24' />
            <span className='bg-gradient-to-r from-olive-700 to-olive-600 bg-clip-text text-transparent text-lg sm:text-xl font-bold hidden xs:inline'>Stom-Line.ru</span>
          </a>

          <nav className="hidden md:flex md:gap-8">
            {navItems.map((item) => (
              <a
                key={item.title}
                href={item.href}
                className="group relative text-sm font-medium text-olive-700 transition-all duration-300 hover:text-olive-600 hover:scale-105"
              >
                {item.title}
                <span className="absolute -bottom-1 left-0 h-[2px] w-0 bg-gradient-to-r from-olive-500 to-olive-600 transition-all duration-300 group-hover:w-full shadow-sm" />
              </a>
            ))}
          </nav>

          <div className="flex items-center gap-2">
            {/* Переключатель версии для слабовидящих */}
            <AccessibilityToggle />

            {/* Компонент поиска для десктопа */}
            <Search buttonVariant="ghost" buttonSize="icon" />

            {/* Мобильное меню */}
            <div className="md:hidden">
              <Drawer>
                <DrawerTrigger asChild>
                  <Button variant="ghost" size="icon" className="inline-flex items-center justify-center rounded-full bg-gradient-to-br from-olive-100/60 to-olive-200/60 p-2 text-olive-700 backdrop-blur-sm hover:from-olive-200/70 hover:to-olive-300/70 hover:text-olive-600 focus:outline-none shadow-lg transition-all duration-300 hover:scale-105">
                    <Menu className="h-6 w-6" />
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="bg-white">
                  <DrawerHeader>
                    <DrawerTitle className="text-olive-800">Меню</DrawerTitle>
                  </DrawerHeader>

                  {/* Поиск и доступность в мобильном меню */}
                  <div className="px-4 pt-2 pb-2 space-y-2">
                    <Button
                      variant="outline"
                      className="w-full justify-start text-olive-700 border-olive-200"
                      onClick={() => {
                        // Закрываем drawer
                        const closeButton = document.querySelector('.drawer-close') as HTMLButtonElement;
                        if (closeButton) closeButton.click();

                        // Открываем поиск с небольшой задержкой
                        setTimeout(() => {
                          const searchButton = document.querySelector('.DocSearch-Button') as HTMLButtonElement;
                          if (searchButton) searchButton.click();
                        }, 300);
                      }}
                    >
                      <SearchIcon className="mr-2 h-4 w-4" />
                      Поиск по сайту
                    </Button>

                    {/* Переключатель доступности в мобильном меню */}
                    <div className="w-full flex justify-center py-2">
                      <AccessibilityToggle />
                    </div>
                  </div>

                  {/* Навигация в мобильном меню */}
                  <div className="px-4 pb-4 space-y-3">
                    {navItems.map((item) => (
                      <DrawerClose asChild key={item.title}>
                        <a
                          href={item.href}
                          className="text-olive-700 hover:bg-olive-200/50 hover:text-olive-900 block rounded-lg px-4 py-3 text-base font-medium transition-colors"
                        >
                          {item.title}
                        </a>
                      </DrawerClose>
                    ))}
                  </div>

                  <DrawerFooter>
                    <Button className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white w-full shadow-lg">
                      <Phone className="mr-2 h-4 w-4" /> Записаться на прием
                    </Button>
                  </DrawerFooter>
                </DrawerContent>
              </Drawer>
            </div>
          </div>
        </div>
      </header>
    </>
  )
}
