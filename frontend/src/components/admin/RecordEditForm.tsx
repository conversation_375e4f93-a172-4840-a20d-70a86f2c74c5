import * as React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>enderer } from './FieldRenderer';

interface Field {
  id: string;
  name: string;
  type: string;
  required?: boolean;
  hidden?: boolean;
  system?: boolean;
  collectionId?: string;
  maxSelect?: number;
  options?: { maxSelect?: number };
  [key: string]: any;
}

interface CollectionSchema {
  id: string;
  name: string;
  type: string;
  schema: Field[];
}

interface RecordEditFormProps {
  collection: string;
  id: string;
  initialRecord: Record<string, any>;
  pbUrl: string;
  token: string;
}

interface RelationData {
  [fieldName: string]: Array<{ id: string; [key: string]: any }>;
}

export const RecordEditForm: React.FC<RecordEditFormProps> = ({
  collection,
  id,
  initialRecord,
  pbUrl,
  token,
}) => {
  const [schema, setSchema] = React.useState<Field[]>([]);
  const [formData, setFormData] = React.useState<Record<string, any>>(initialRecord);
  
  console.log('Initial record data:', initialRecord);
  const [relationData, setRelationData] = React.useState<RelationData>({});
  const [loading, setLoading] = React.useState(false);
  const [saving, setSaving] = React.useState(false);
  const [error, setError] = React.useState('');
  const [success, setSuccess] = React.useState(false);

  console.log('RecordEditForm props:', { collection, id, initialRecord, pbUrl, token: token ? 'есть' : 'нет' });

  // Загружаем схему коллекции
  React.useEffect(() => {
    const fetchSchema = async () => {
      setLoading(true);
      setError('');
      
      try {
        // Сначала попробуем получить схему из pb_schema.json или используем статическую схему
        const response = await fetch('/pb_schema.json');
        
        if (response.ok) {
          const collections = await response.json();
          const currentCollection = collections.find((c: any) => c.name === collection);
          
          if (currentCollection && currentCollection.fields) {
            // Фильтруем поля для отображения
            const visibleFields = currentCollection.fields.filter((field: any) => 
              !field.hidden && 
              !field.system && 
              field.name !== 'id' &&
              field.name !== 'created' &&
              field.name !== 'updated'
            );
            
            setSchema(visibleFields);
          } else {
            // Если коллекция не найдена в схеме, создаем базовые поля на основе записи
            const recordKeys = Object.keys(initialRecord || {});
            const basicFields = recordKeys
              .filter(key => !['id', 'created', 'updated', 'collectionId', 'collectionName'].includes(key))
              .map(key => ({
                id: key,
                name: key,
                type: inferFieldType(initialRecord[key]),
                required: false,
                hidden: false,
                system: false
              }));
            
            setSchema(basicFields);
          }
        } else {
          throw new Error('Не удалось загрузить схему');
        }
      } catch (err) {
        console.error('Ошибка загрузки схемы:', err);
        
        // Fallback: создаем схему на основе данных записи
        if (initialRecord) {
          const recordKeys = Object.keys(initialRecord);
          const basicFields = recordKeys
            .filter(key => !['id', 'created', 'updated', 'collectionId', 'collectionName'].includes(key))
            .map(key => ({
              id: key,
              name: key,
              type: inferFieldType(initialRecord[key]),
              required: false,
              hidden: false,
              system: false
            }));
          
          setSchema(basicFields);
        } else {
          setError('Не удалось загрузить схему коллекции');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSchema();
  }, [collection, pbUrl, token, initialRecord]);

  // Нормализуем данные для мультиселектов после загрузки схемы
  React.useEffect(() => {
    if (schema.length > 0) {
      const normalizedData = { ...initialRecord };
      
      schema.forEach(field => {
        if (field.type === 'relation') {
          const maxSelectValue = field.options?.maxSelect !== undefined ? field.options.maxSelect : field.maxSelect;
          const isMultiple = maxSelectValue === 0 || (maxSelectValue !== undefined && maxSelectValue > 1);
          
          if (isMultiple) {
            const currentValue = initialRecord[field.name];
            console.log(`Нормализация поля ${field.name}:`, currentValue);
            
            if (typeof currentValue === 'string' && currentValue) {
              // Если это строка с ID через запятую
              normalizedData[field.name] = currentValue.split(',').map(id => id.trim()).filter(Boolean);
            } else if (Array.isArray(currentValue)) {
              // Если уже массив
              normalizedData[field.name] = currentValue;
            } else if (currentValue) {
              // Если одиночное значение, делаем массивом
              normalizedData[field.name] = [currentValue];
            } else {
              // Если пустое значение
              normalizedData[field.name] = [];
            }
          }
        }
      });
      
      setFormData(normalizedData);
      console.log('Normalized data:', normalizedData);
    }
  }, [schema, initialRecord]);

  // Функция для определения типа поля на основе значения
  const inferFieldType = (value: any): string => {
    if (typeof value === 'boolean') return 'bool';
    if (typeof value === 'number') return 'number';
    if (typeof value === 'string') {
      if (value.includes('@') && value.includes('.')) return 'email';
      if (value.startsWith('http')) return 'url';
      if (value.match(/^\d{4}-\d{2}-\d{2}/)) return 'date';
      if (value.length > 100) return 'editor';
      return 'text';
    }
    if (Array.isArray(value)) return 'relation';
    return 'text';
  };

  // Загружаем данные для relation полей
  React.useEffect(() => {
    const fetchRelationData = async () => {
      const relationFields = schema.filter(field => field.type === 'relation');
      if (relationFields.length === 0) return;

      const relationPromises = relationFields.map(async (field) => {
        // Ищем collectionId в разных местах
        const targetCollection = field.options?.collectionId || field.collectionId;
        
        if (!targetCollection) {
          console.warn(`Не найден targetCollection для поля ${field.name}`);
          return null;
        }
        
        try {
          const response = await fetch(
            `${pbUrl}/api/collections/${targetCollection}/records?perPage=500&sort=created`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
              },
            }
          );

          if (response.ok) {
            const data = await response.json();
            return { fieldName: field.name, records: data.items || [] };
          } else {
            console.error(`Ошибка ${response.status} при загрузке ${targetCollection}`);
          }
        } catch (err) {
          console.error(`Ошибка загрузки данных для поля ${field.name}:`, err);
        }
        return null;
      });

      const results = await Promise.all(relationPromises);
      const newRelationData: RelationData = {};
      
      results.forEach(result => {
        if (result) {
          newRelationData[result.fieldName] = result.records;
        }
      });

      setRelationData(newRelationData);
    };

    if (schema.length > 0) {
      fetchRelationData();
    }
  }, [schema, pbUrl, token]);

  const handleFieldChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError('');
    setSuccess(false);

    try {
      // Подготавливаем данные для отправки
      const submitData = new FormData();
      
      schema.forEach(field => {
        const value = formData[field.name];
        
        if (value === undefined || value === null) return;
        
        if (field.type === 'file') {
          if (value instanceof File) {
            submitData.append(field.name, value);
          }
          // Если это строка (существующий файл), не добавляем в FormData
        } else if (field.type === 'relation') {
          // Определяем тип relation поля
          const maxSelectValue = field.options?.maxSelect !== undefined ? field.options.maxSelect : field.maxSelect;
          const isMultiple = maxSelectValue === 0 || (maxSelectValue !== undefined && maxSelectValue > 1);
          
          // Для relation полей отправляем как строку или массив
          if (isMultiple && Array.isArray(value)) {
            // Для мультиселекта отправляем каждое значение отдельно
            value.forEach(val => {
              if (val) submitData.append(field.name, String(val));
            });
          } else if (value) {
            // Для одиночного селекта или простого значения
            submitData.append(field.name, String(value));
          }
        } else {
          submitData.append(field.name, String(value));
        }
      });

      const response = await fetch(`${pbUrl}/api/collections/${collection}/records/${id}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: submitData,
      });

      if (response.ok) {
        const updatedRecord = await response.json();
        setFormData(updatedRecord);
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Ошибка при сохранении');
      }
    } catch (err) {
      setError('Ошибка подключения к серверу');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Загрузка схемы...</p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {schema.map((field) => (
        <FieldRenderer
          key={field.name}
          field={field}
          value={formData[field.name]}
          onChange={(value) => handleFieldChange(field.name, value)}
          relationOptions={relationData[field.name] || []}
          pbUrl={pbUrl}
          collection={collection}
          recordId={id}
          disabled={saving}
        />
      ))}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Ошибка</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Успех</h3>
              <div className="mt-2 text-sm text-green-700">Данные успешно сохранены</div>
            </div>
          </div>
        </div>
      )}

      <div className="flex gap-4 pt-6 border-t">
        <Button type="submit" disabled={saving} className="flex-1">
          {saving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Сохранение...
            </>
          ) : (
            'Сохранить изменения'
          )}
        </Button>
        
        <Button 
          type="button" 
          variant="outline" 
          onClick={() => window.history.back()}
          disabled={saving}
        >
          Отмена
        </Button>
      </div>
    </form>
  );
};
