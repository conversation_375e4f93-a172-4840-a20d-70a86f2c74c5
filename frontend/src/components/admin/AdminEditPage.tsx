import * as React from 'react';
import { AdminAuth } from './AdminAuth';
import { RecordEditForm } from './RecordEditForm';

interface AdminEditPageProps {
  collection: string;
  id: string;
  initialRecord: any;
  pbUrl: string;
}

export const AdminEditPage: React.FC<AdminEditPageProps> = ({
  collection,
  id,
  initialRecord,
  pbUrl,
}) => {
  const [isAuthenticated, setIsAuthenticated] = React.useState<boolean>(false);
  const [token, setToken] = React.useState<string>('');
  const [loading, setLoading] = React.useState<boolean>(true);

  React.useEffect(() => {
    const savedToken = localStorage.getItem('pb_token');
    console.log('Проверка токена:', savedToken ? 'найден' : 'не найден');

    if (savedToken) {
      setToken(savedToken);
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const handleAuth = (authToken: string) => {
    console.log('Авторизация успешна');
    setToken(authToken);
    setIsAuthenticated(true);
    localStorage.setItem('pb_token', authToken);

    // Устанавливаем cookie для middleware
    document.cookie = `pb_token=${authToken}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Lax`;
    console.log('Токен синхронизирован с cookies для middleware');
  };

  const handleLogout = () => {
    setToken('');
    setIsAuthenticated(false);
    localStorage.removeItem('pb_token');

    // Удаляем cookie
    document.cookie = 'pb_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    console.log('Токен удален из localStorage и cookies');
  };

  // Показываем загрузку пока проверяем токен
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Проверка авторизации...</p>
        </div>
      </div>
    );
  }

  // Проверяем авторизацию
  if (!isAuthenticated) {
    return (
      <div>
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <p className="text-blue-700 text-sm">
            Для редактирования записей необходима авторизация
          </p>
        </div>
        <AdminAuth onAuth={handleAuth} pbUrl={pbUrl} />
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6 pb-4 border-b">
        <div>
          <button
            onClick={() => window.history.back()}
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mb-2"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Назад
          </button>
        </div>
        <button
          onClick={handleLogout}
          className="text-sm text-red-600 hover:text-red-800"
        >
          Выйти
        </button>
      </div>

      <RecordEditForm
        collection={collection}
        id={id}
        initialRecord={initialRecord}
        pbUrl={pbUrl}
        token={token || 'test-token'}
      />
    </div>
  );
};
