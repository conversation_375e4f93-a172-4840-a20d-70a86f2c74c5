import React from 'react'
import { EditorContent, useEditor } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import TextAlign from '@tiptap/extension-text-align'
import Link from '@tiptap/extension-link'
import Underline from '@tiptap/extension-underline'
import Image from '@tiptap/extension-image'
import { Button } from '../ui/button'

interface TiptapEditorWrapperProps {
  id?: string
  value: string
  onChange: (value: string) => void
  readOnly?: boolean
  style?: React.CSSProperties
}

export const TiptapEditorWrapper: React.FC<TiptapEditorWrapperProps> = ({ id, value, onChange, readOnly = false, style = {} }) => {
  const editor = useEditor({
    extensions: [StarterKit, TextAlign.configure({ types: ['heading', 'paragraph'] }), Link, Underline, Image],
    content: value,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
    }
  })

  React.useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value || '', false)
    }
    // eslint-disable-next-line
  }, [value])

  return (
    <div id={id} style={style} className='tiptap-editor min-h-[120px] rounded border bg-white p-2'>
      {editor && (
        <div className='mb-2 flex flex-wrap gap-1'>
          <Button onClick={() => editor.chain().focus().toggleBold().run()} className={editor.isActive('bold') ? 'rounded border border-b-4 border-gray-700 px-2 font-bold' : 'px-2'}>
            <b>B</b>
          </Button>
          <Button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'rounded border border-b-4 border-gray-700 px-2 italic' : 'px-2'}
          >
            <i>I</i>
          </Button>
          <Button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={editor.isActive('underline') ? 'rounded border border-b-4 border-gray-700 px-2 underline' : 'px-2'}
          >
            U
          </Button>
          <Button
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            className={editor.isActive('heading', { level: 1 }) ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            H1
          </Button>
          <Button
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            className={editor.isActive('heading', { level: 2 }) ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            H2
          </Button>
          <Button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={editor.isActive('bulletList') ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            • Список
          </Button>
          <Button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={editor.isActive('orderedList') ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            1. Список
          </Button>
          <Button
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={editor.isActive({ textAlign: 'left' }) ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            ⯇
          </Button>
          <Button
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={editor.isActive({ textAlign: 'center' }) ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            ≡
          </Button>
          <Button
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={editor.isActive({ textAlign: 'right' }) ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            ⯈
          </Button>
          <Button onClick={() => editor.chain().focus().unsetAllMarks().clearNodes().run()} className='px-2'>
            Очистить
          </Button>
          <Button
            onClick={() => {
              const url = window.prompt('Введите ссылку')
              if (url) editor.chain().focus().setLink({ href: url }).run()
            }}
            className={editor.isActive('link') ? 'rounded border border-b-4 border-gray-700 px-2' : 'px-2'}
          >
            🔗
          </Button>
          <Button onClick={() => editor.chain().focus().unsetLink().run()} className='px-2'>
            ⛔️
          </Button>
          <Button
            onClick={() => {
              const url = window.prompt('Вставьте ссылку на изображение')
              if (url) editor.chain().focus().setImage({ src: url }).run()
            }}
            className='px-2'
          >
            🖼️
          </Button>
        </div>
      )}
      <EditorContent editor={editor} />
    </div>
  )
}
