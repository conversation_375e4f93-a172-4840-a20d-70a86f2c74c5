import * as React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TiptapEditorWrapper } from './TiptapEditorWrapper';

interface Field {
  id: string;
  name: string;
  type: string;
  required?: boolean;
  options?: {
    maxSelect?: number;
    collectionId?: string;
    [key: string]: any;
  };
  collectionId?: string;
  maxSelect?: number;
  [key: string]: any;
}

interface FieldRendererProps {
  field: Field;
  value: any;
  onChange: (value: any) => void;
  relationOptions?: Array<{ id: string; [key: string]: any }>;
  pbUrl: string;
  collection: string;
  recordId: string;
  disabled?: boolean;
}

export const FieldRenderer: React.FC<FieldRendererProps> = ({
  field,
  value,
  onChange,
  relationOptions = [],
  pbUrl,
  collection,
  recordId,
  disabled = false,
}) => {
  const fieldId = `field-${field.name}`;
  const isRequired = field.required || false;
  
  // Функция для получения читаемого названия поля
  const getFieldLabel = () => {
    // Мапинг названий полей на русский
    const labelMap: Record<string, string> = {
      name: 'Название',
      title: 'Заголовок',
      surname: 'Фамилия',
      patronymic: 'Отчество',
      slug: 'URL слаг',
      position: 'Должность',
      short_description: 'Краткое описание',
      biography: 'Биография',
      content: 'Содержимое',
      description: 'Описание',
      photo: 'Фото',
      image: 'Изображение',
      featured_image: 'Главное изображение',
      gallery: 'Галерея',
      specializations: 'Специализации',
      services: 'Услуги',
      category: 'Категория',
      service: 'Услуга',
      experience: 'Опыт работы',
      clinic: 'Клиника',
      meta_title: 'SEO заголовок',
      meta_description: 'SEO описание',
      is_featured: 'Рекомендуемый',
      is_published: 'Опубликовано',
      sort_order: 'Порядок сортировки',
      date: 'Дата',
      question: 'Вопрос',
      answer: 'Ответ',
      price: 'Цена',
      price_prefix: 'Префикс цены',
      price_suffix: 'Суффикс цены',
      parent_slug: 'Родительская страница',
    };
    
    return labelMap[field.name] || field.name;
  };

  const renderField = () => {
    switch (field.type) {
      case 'text':
        return (
          <Input
            id={fieldId}
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            placeholder={`Введите ${getFieldLabel().toLowerCase()}`}
          />
        );

      case 'email':
        return (
          <Input
            id={fieldId}
            type="email"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            placeholder="<EMAIL>"
          />
        );

      case 'url':
        return (
          <Input
            id={fieldId}
            type="url"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            placeholder="https://example.com"
          />
        );

      case 'number':
        return (
          <Input
            id={fieldId}
            type="number"
            value={value !== undefined && value !== null ? value : ''}
            onChange={(e) => {
              const val = e.target.value;
              onChange(val === '' ? null : Number(val));
            }}
            disabled={disabled}
            placeholder="0"
          />
        );

      case 'date':
        // Конвертируем дату в нужный формат для input[type="date"]
        const dateValue = value ? new Date(value).toISOString().split('T')[0] : '';
        return (
          <Input
            id={fieldId}
            type="date"
            value={dateValue}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
          />
        );

      case 'bool':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={!!value}
              onCheckedChange={(checked) => onChange(checked)}
              disabled={disabled}
            />
            <label htmlFor={fieldId} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              {getFieldLabel()}
            </label>
          </div>
        );

      case 'editor':
        return (
          <TiptapEditorWrapper
            id={fieldId}
            value={value || ''}
            onChange={onChange}
            readOnly={disabled}
            style={{ minHeight: 200 }}
          />
        );

      case 'relation':
        // В PocketBase: maxSelect = 1 означает одиночный выбор, maxSelect = 0 или > 1 означает множественный выбор
        const maxSelectValue = field.options?.maxSelect !== undefined ? field.options.maxSelect : field.maxSelect;
        const isMultiple = maxSelectValue === 0 || (maxSelectValue !== undefined && maxSelectValue > 1);
        
        console.log(`Поле ${field.name}: maxSelect=${maxSelectValue}, isMultiple=${isMultiple}`);

        if (isMultiple) {
          // Мультиселект для связей
          const selectedValues = Array.isArray(value) ? value : (value ? [value] : []);
          
          return (
            <div className="space-y-2">
              <div className="border rounded-md p-3 min-h-[100px] max-h-[200px] overflow-y-auto">
                {relationOptions.length === 0 ? (
                  <p className="text-gray-500 text-sm">Нет доступных вариантов</p>
                ) : (
                  <div className="space-y-2">
                    {relationOptions.map((option) => {
                      const optionLabel = option.name || option.title || option.question || option.id;
                      const isSelected = selectedValues.includes(option.id);
                      
                      return (
                        <div key={option.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`${fieldId}-${option.id}`}
                            checked={isSelected}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                onChange([...selectedValues, option.id]);
                              } else {
                                onChange(selectedValues.filter(id => id !== option.id));
                              }
                            }}
                            disabled={disabled}
                          />
                          <label 
                            htmlFor={`${fieldId}-${option.id}`}
                            className="text-sm leading-none cursor-pointer"
                          >
                            {optionLabel}
                          </label>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500">
                Выбрано: {selectedValues.length}
              </p>
            </div>
          );
        } else {
          // Одиночный селект
          return (
            <Select
              value={value || '__empty__'}
              onValueChange={(val) => onChange(val === '__empty__' ? '' : val)}
              disabled={disabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Выберите вариант" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__empty__">Не выбрано</SelectItem>
                {relationOptions.map((option) => {
                  const optionLabel = option.name || option.title || option.question || option.id;
                  return (
                    <SelectItem key={option.id} value={option.id}>
                      {optionLabel}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          );
        }

      case 'file':
        const hasFile = value && typeof value === 'string';
        const fileUrl = hasFile ? `${pbUrl}/api/files/${collection}/${recordId}/${value}` : null;
        const isImage = fileUrl && /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(value);

        return (
          <div className="space-y-3">
            <Input
              id={fieldId}
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                onChange(file || null);
              }}
              disabled={disabled}
            />
            
            {hasFile && (
              <div className="border rounded-lg p-3 bg-gray-50">
                <p className="text-sm text-gray-600 mb-2">Текущий файл:</p>
                {isImage ? (
                  <div>
                    <img 
                      src={fileUrl} 
                      alt={field.name}
                      className="max-w-xs max-h-40 rounded border mb-2"
                    />
                    <a 
                      href={fileUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Открыть в новой вкладке
                    </a>
                  </div>
                ) : (
                  <a 
                    href={fileUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    {value} (скачать)
                  </a>
                )}
              </div>
            )}
          </div>
        );

      default:
        // Для неизвестных типов используем textarea
        return (
          <Textarea
            id={fieldId}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            rows={3}
            placeholder={`Введите ${getFieldLabel().toLowerCase()}`}
          />
        );
    }
  };

  // Для bool полей не показываем отдельный label, так как он встроен в checkbox
  if (field.type === 'bool') {
    return <div className="space-y-2">{renderField()}</div>;
  }

  return (
    <div className="space-y-2">
      <label htmlFor={fieldId} className="block text-sm font-medium text-gray-700">
        {getFieldLabel()}
        {isRequired && <span className="text-red-500 ml-1">*</span>}
      </label>
      {renderField()}
    </div>
  );
};
