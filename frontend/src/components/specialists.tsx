'use client'

import { useRef } from 'react'
import { motion, useScroll, useTransform, useInView } from 'framer-motion'
import { Phone, Calendar, Star, Award, GraduationCap, User, CheckCircle2, ArrowRight, Stethoscope } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { BorderBeam } from '@/components/border-beam'
import { cn } from '@/lib/utils'
import { TracingBeam } from '@/components/tracing-beam'
import { EditButton } from '@/components/admin/EditButton'

import { type Doctor } from '@/lib/api'

interface Props {
  specialists: Doctor[]
}



function SpecialistSection({ specialist, index }: { specialist: Doctor; index: number }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, margin: '-100px 0px' })

  const isEven = index % 2 === 0

  // Формируем полное имя
  const fullName = `${specialist.surname} ${specialist.name} ${specialist.patronymic || ''}`.trim()

  // Получаем специализации из расширенных данных
  const specializations = Array.isArray(specialist.expand?.specializations)
    ? specialist.expand.specializations.map(spec => spec.name || '')
    : []

  // Получаем услуги из расширенных данных
  const services = Array.isArray(specialist.expand?.services)
    ? specialist.expand.services.map(service => service.title || service.name || '')
    : []

  return (
    <motion.div
      ref={containerRef}
      id={`specialist-${specialist.id}`}
      className={cn('relative min-h snap-start py-24 md:py-32', isEven ? 'from-olive-50/50 bg-gradient-to-r to-transparent' : '')}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <EditButton 
        collection="doctors" 
        id={specialist.id} 
        position="top-right" 
        variant="text" 
        size="sm"
        className="bg-white/90 border border-gray-200 shadow-sm"
      />
      <div className='container mx-auto px-4 md:px-6'>
        <div className={`grid grid-cols-1 items-start gap-8 lg:grid-cols-2 lg:gap-16 ${isEven ? '' : 'lg:flex-row-reverse'}`}>
          {/* Specialist image with 3D effect */}
          <motion.div
            className='relative aspect-[3/4] overflow-hidden rounded-2xl shadow-2xl'
            initial={{ opacity: 0, x: isEven ? -50 : 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: isEven ? -50 : 50 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <div className='from-olive-950/80 absolute inset-0 z-10 bg-gradient-to-t via-transparent to-transparent' />
            <img
              src={specialist.photo ? `${import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru'}/api/files/doctors/${specialist.id}/${specialist.photo}` : '/placeholder.svg?height=600&width=400'}
              alt={fullName}
              className='w-full h-full object-cover transform transition-transform duration-700 hover:scale-105'
            />
            <div className='absolute right-0 bottom-0 left-0 z-20 p-6'>
              <div className='mb-4 flex flex-wrap gap-2'>
                <span className='bg-olive-600/60 rounded-full px-3 py-1 text-sm font-medium text-white backdrop-blur-sm'>
                  {specialist.position}
                </span>
                {specializations.slice(0, 2).map((specialty, idx) => (
                  <span
                    key={`specialty-card-${specialist.id}-${idx}`}
                    className='bg-olive-500/40 rounded-full px-3 py-1 text-sm font-medium text-white backdrop-blur-sm'
                  >
                    {specialty}
                  </span>
                ))}
              </div>
            </div>
            <BorderBeam colorFrom='#4D8C29' colorTo='#85C028' size={60} duration={8} delay={index * 0.5} />
          </motion.div>

          {/* Specialist info */}
          <motion.div
            className='flex flex-col justify-start'
            initial={{ opacity: 0, x: isEven ? 50 : -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: isEven ? 50 : -50 }}
            transition={{ duration: 0.7, delay: 0.4 }}
          >
            <div className='mb-2 flex items-center'>
              <span className='text-olive-600 font-medium'>{specialist.position}</span>
              <div className='bg-olive-100 ml-4 flex items-center rounded-full px-3 py-1'>
                <Star className='text-olive-600 mr-1 h-4 w-4' />
                {specialist.experience && (
                  <span className='text-olive-800 text-sm font-medium'>{specialist.experience}</span>
                )}
              </div>
            </div>

            <h2 className='mb-6 text-3xl font-bold md:text-4xl'>{fullName}</h2>

            {/* About section */}
            {specialist.short_description && (
              <div className='border-olive-100 mb-6 rounded-xl border bg-white p-6 shadow-sm'>
                <div className='mb-3 flex items-center'>
                  <User className='text-olive-600 mr-2 h-5 w-5' />
                  <h3 className='text-olive-800 text-lg font-semibold'>О специалисте</h3>
                </div>
                <div className='text-olive-800 text-lg leading-relaxed' dangerouslySetInnerHTML={{ __html: specialist.short_description || '' }} />
              </div>
            )}

            {/* Education section */}
            <div className='border-olive-100 mb-6 rounded-xl border bg-white p-6 shadow-sm'>
              <div className='mb-3 flex items-center'>
                <GraduationCap className='text-olive-600 mr-2 h-5 w-5' />
                <h3 className='text-olive-800 text-lg font-semibold'>Образование</h3>
              </div>
              <p className='text-olive-800 text-lg leading-relaxed'>Высшее медицинское образование</p>
            </div>

            {/* Biography section */}
            {specialist.biography && (
              <div className='border-olive-100 mb-6 rounded-xl border bg-white p-6 shadow-sm'>
                <div className='mb-3 flex items-center'>
                  <Award className='text-olive-600 mr-2 h-5 w-5' />
                  <h3 className='text-olive-800 text-lg font-semibold'>Достижения и биография</h3>
                </div>
                <div className='text-olive-800 text-lg leading-relaxed' dangerouslySetInnerHTML={{ __html: specialist.biography || '' }} />
              </div>
            )}

            {/* Specializations section */}
            {specializations.length > 0 && (
              <div className='border-olive-100 mb-6 rounded-xl border bg-white p-6 shadow-sm'>
                <div className='mb-3 flex items-center'>
                  <Star className='text-olive-600 mr-2 h-5 w-5' />
                  <h3 className='text-olive-800 text-lg font-semibold'>Специализации</h3>
                </div>
                <div className='flex flex-wrap gap-2'>
                  {specializations.map((specialty, index) => (
                    <span
                      key={`specialty-${specialist.id}-${index}`}
                      className='bg-olive-100 rounded-full px-3 py-1 text-sm font-medium text-olive-800'
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Services section */}
            {services.length > 0 && (
              <div className='border-olive-100 mb-6 rounded-xl border bg-white p-6 shadow-sm'>
                <div className='mb-3 flex items-center justify-between'>
                  <div className='flex items-center'>
                    <Stethoscope className='text-olive-600 mr-2 h-5 w-5' />
                    <h3 className='text-olive-800 text-lg font-semibold'>Услуги</h3>
                  </div>
                  <a
                    href={`/services?specialist=${specialist.id}`}
                    className='text-olive-600 flex items-center text-sm font-medium hover:underline'
                  >
                    Все услуги <ArrowRight className='ml-1 h-3 w-3' />
                  </a>
                </div>

                <div className='space-y-3 mb-4'>
                  {services.slice(0, 3).map((service, index) => (
                    <div
                      key={`service-${specialist.id}-${index}`}
                      className='flex items-start'
                    >
                      <CheckCircle2 className='text-olive-600 mt-1 mr-3 h-4 w-4 flex-shrink-0' />
                      <div>
                        <a
                          href={`/services#${service.toLowerCase().replace(/\s+/g, '-')}`}
                          className='text-olive-800 font-medium hover:text-olive-600 hover:underline'
                        >
                          {service}
                        </a>
                      </div>
                    </div>
                  ))}
                </div>

                {services.length > 3 && (
                  <div className='flex justify-end'>
                    <a
                      href={`/services?specialist=${specialist.id}`}
                      className='text-olive-600 text-sm font-medium hover:underline flex items-center'
                    >
                      Ещё {services.length - 3} услуг <ArrowRight className='ml-1 h-3 w-3' />
                    </a>
                  </div>
                )}
              </div>
            )}

            <div className='mt-4 flex flex-col gap-4 sm:flex-row'>
              <Button className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl'>
                <Phone className='mr-2 h-4 w-4' /> Записаться на прием
              </Button>
              <Button variant='outline' className='border-olive-200 text-olive-700 hover:bg-olive-50 shadow-lg'>
                <Calendar className='mr-2 h-4 w-4' /> Расписание
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}

export const Specialists = ({ specialists }: Props) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start']
  })

  const opacity = useTransform(scrollYProgress, [0, 0.05], [1, 1])
  const y = useTransform(scrollYProgress, [0, 0.05], [0, 0])

  return (
    <div className="relative w-full" ref={containerRef}>
      <TracingBeam>
        {/* Hero section */}
        <motion.div className='relative flex h-[30vh] items-center justify-center overflow-hidden md:h-[40vh]' style={{ opacity, y }}>
          <div className='bg-olive-600/10 absolute inset-0 backdrop-blur-sm' />
          <div className='relative z-10 container px-4 md:px-6'>
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }} className='text-center'>
              <h1 className='text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-8'>Наши специалисты</h1>
              <p className='text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed'>Команда профессионалов, которая заботится о здоровье вашей улыбки</p>
            </motion.div>
          </div>
        </motion.div>

        {/* Main content with tracing beam */}
        <div className='snap-y snap-mandatory scroll-pt-16'>
          <div className='relative'>
            <div className='relative'>
              {specialists.map((specialist, index) => (
                <SpecialistSection key={specialist.id} specialist={specialist} index={index} />
              ))}
            </div>
          </div>
        </div>

        {/* Team summary section */}
        <div className='snap-start py-16 md:py-24'>
          <div className='container mx-auto px-4 md:px-6'>
            <div className='mx-auto max-w-3xl text-center'>
              <h2 className='mb-6 text-3xl font-bold md:text-4xl'>Почему выбирают наших специалистов</h2>
              <p className='text-olive-800 mb-12 text-lg'>Наша команда — это сочетание опыта, профессионализма и современного подхода к стоматологии</p>

              <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
                {[
                  'Более 70 лет суммарного опыта',
                  'Регулярное повышение квалификации',
                  'Использование передовых технологий',
                  'Индивидуальный подход к каждому пациенту',
                  'Безболезненное лечение',
                  'Гарантия на все виды работ'
                ].map((item, index) => (
                  <div key={`team-feature-${index}`} className='relative flex items-center overflow-hidden rounded-lg bg-white p-4 shadow-sm'>
                    <CheckCircle2 className='text-olive-600 mr-3 h-5 w-5 flex-shrink-0' />
                    <span className='text-olive-900'>{item}</span>
                  </div>
                ))}
              </div>

              <Button className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 mt-12 text-white shadow-xl'>Познакомиться с нашей командой лично</Button>
            </div>
          </div>
        </div>
      </TracingBeam>
    </div>
  )
}

// Simplified tracing beam component

