import { useEffect, useRef } from "react"
import { Facebook, Instagram, Twitter } from "lucide-react"

export default function Footer() {
  const grassRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!grassRef.current) return

    const handleMouseMove = (e: MouseEvent) => {
      const grassBlades = grassRef.current?.querySelectorAll(".grass-blade")
      if (!grassBlades) return

      const mouseX = e.clientX / window.innerWidth

      grassBlades.forEach((blade, index) => {
        const bladeElement = blade as HTMLElement
        const factor = (index % 5) * 0.2 - 0.5
        bladeElement.style.transform = `rotate(${factor * mouseX * 10}deg)`
      })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => window.removeEventListener("mousemove", handleMouseMove)
  }, [])

  return (
    <footer className="relative w-full bg-gradient-to-br from-olive-50 via-olive-100/80 to-olive-50 text-olive-700">
      <div className="container mx-auto px-4 py-16 md:px-6">
        <div className="grid grid-cols-1 gap-8 sm:gap-12 md:grid-cols-4">
          <div className="md:col-span-2">
            <div className="flex items-center gap-2 justify-center sm:justify-start">
              {/* <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-olive-400 to-olive-600">
                <span className="text-lg font-bold text-white">S</span>
              </div> */}
              <img src="/17a70891-5433-419e-99aa-33350107e7c9-removebg-preview.png" alt="Stom-Line" className="h-12 w-12 sm:h-14 sm:w-14" />
              <h3 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-olive-700 to-olive-600 bg-clip-text text-transparent">Stom-Line.ru</h3>
            </div>
            <p className="mt-4 max-w-md text-sm sm:text-base text-olive-600 text-center sm:text-left">
              Инновационная стоматологическая клиника, сочетающая передовые технологии с заботой о каждом пациенте. Мы
              создаем улыбки будущего уже сегодня.
            </p>

          </div>
          <div className="text-center sm:text-left">
            <h3 className="mb-4 text-base sm:text-lg font-bold bg-gradient-to-r from-olive-700 to-olive-600 bg-clip-text text-transparent">Контакты</h3>
            <ul className="space-y-2 sm:space-y-3 text-olive-600 text-sm sm:text-base">
              <li className="flex items-start gap-2 justify-center sm:justify-start">
                <span className="mt-1 block h-1.5 w-1.5 rounded-full bg-olive-500"></span>
                <span>проспект Кольский, д. 202</span>
              </li>
              <li className="flex items-start gap-2 justify-center sm:justify-start">
                <span className="mt-1 block h-1.5 w-1.5 rounded-full bg-olive-500"></span>
                <span>(8152) 52-57-08</span>
              </li>
              <li className="flex items-start gap-2 justify-center sm:justify-start">
                <span className="mt-1 block h-1.5 w-1.5 rounded-full bg-olive-500"></span>
                {/* <span><EMAIL></span> */}
                <span><EMAIL></span>
              </li>
            </ul>
          </div>
          <div className="text-center sm:text-left">
            <h3 className="mb-4 text-base sm:text-lg font-bold bg-gradient-to-r from-olive-700 to-olive-600 bg-clip-text text-transparent">Часы работы</h3>
            <ul className="space-y-2 sm:space-y-3 text-olive-600 text-sm sm:text-base">
              <li className="flex justify-between">
                <span>Пн-Пт:</span>
                <span>9:00 - 20:00</span>
              </li>
              <li className="flex justify-between">
                <span>Сб:</span>
                <span>10:00 - 18:00</span>
              </li>
              <li className="flex justify-between">
                <span>Вс:</span>
                <span>Выходной</span>
              </li>
            </ul>
          </div>
        </div>
        <div className="mt-8 border-t border-olive-300/50 pt-8">
          <div className="mb-8 px-2 sm:px-4 md:px-6">
            <div className="rounded-lg bg-gradient-to-br from-white/90 via-white/80 to-olive-50/30 p-3 sm:p-6 shadow-lg backdrop-blur-sm border border-olive-200/30">
              <h4 className="mb-2 sm:mb-3 text-xs sm:text-sm font-semibold text-olive-800 uppercase text-center sm:text-left">НЕОБХОДИМА КОНСУЛЬТАЦИЯ СПЕЦИАЛИСТА</h4>
              <p className="text-xs sm:text-sm text-olive-600 leading-relaxed">
                Обращаем Ваше внимание на то, что данный интернет-сайт носит исключительно информационный характер и ни при каких условиях не является публичной офертой, определяемой положениями ч. 2 ст. 437 Гражданского кодекса Российской Федерации. Для получения подробной информации о стоимости, наименовании и сроках оказания услуг, пожалуйста, обращайтесь по контактным телефонам.
              </p>
            </div>
          </div>
          <div className="text-center space-y-2">
            <div className="flex justify-center gap-4 text-sm text-olive-600 mb-2">
              <a href="/accessibility" className="hover:text-olive-600 underline transition-colors duration-300">
                Версия для слабовидящих
              </a>
              <span>|</span>
              <a href="/privacy" className="hover:text-olive-600 underline transition-colors duration-300">
                Политика конфиденциальности
              </a>
            </div>
            <p className="text-olive-600">© 2008 - {new Date().getFullYear()} Stom-Line.ru. Все права защищены.</p>
          </div>
        </div>
      </div>

      {/* Animated grass - скрыто на мобильных устройствах */}
      <div className="absolute bottom-0 left-0 right-0 h-40 overflow-hidden hidden sm:block">
        <div ref={grassRef} className="absolute bottom-0 left-0 right-0 flex h-40 w-full items-end justify-between">
          {Array.from({ length: window.innerWidth < 768 ? 60 : 120 }).map((_, i) => {
            const height = Math.random() * 100 + 60
            const width = Math.random() * 3 + 1
            const delay = Math.random() * 2
            const duration = Math.random() * 3 + 2

            return (
              <div
                key={i}
                className="grass-blade origin-bottom transition-transform duration-300 ease-out"
                style={{
                  height: `${height}px`,
                  width: `${width}px`,
                  backgroundColor: i % 3 === 0 ? "#a3be67" : i % 3 === 1 ? "#7F8643" : "#bccf8c",
                  borderRadius: "0 100% 0 100%",
                  animation: `sway ${duration}s ease-in-out infinite alternate`,
                  animationDelay: `${delay}s`,
                  transformOrigin: "bottom center",
                }}
              ></div>
            )
          })}
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-r from-olive-500 via-olive-600 to-olive-500"></div>
      </div>
      {/* Простая линия для мобильных устройств */}
      <div className="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-r from-olive-500 via-olive-600 to-olive-500 sm:hidden"></div>
    </footer>
  )
}

