import { useEffect, useState, useRef } from 'react';
import { Search as SearchIcon, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface SearchProps {
  className?: string;
  buttonVariant?: 'default' | 'ghost' | 'outline';
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon';
  mobile?: boolean;
}

interface SearchResult {
  id: string;
  name?: string;
  title?: string;
  question?: string;
  slug?: string;
  content?: string;
  short_description?: string;

  // Поля для врачей
  surname?: string;
  patronymic?: string;
  position?: string;
  experience?: string;
  fullName?: string;

  // Поля для услуг
  category?: string;

  // Поля для цен
  price?: number;
  old_price?: number;
  service_id?: string;
  service_name?: string;
  category_id?: string;
  category_name?: string;

  // Общие поля
  _index: string;
}

export function Search({
  className = '',
  buttonVariant = 'ghost',
  buttonSize = 'icon',
  mobile = false
}: SearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Функция для выполнения поиска
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);

    try {
      // Используем fetch для прямого запроса к MeiliSearch API
      const searchUrl = import.meta.env.PUBLIC_SEARCH_URL || 'https://search.stom-line.ru';
      const apiKey = import.meta.env.PUBLIC_SEARCH_API_KEY || 'Nc040stomline';

      const response = await fetch(`${searchUrl}/indexes/all/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          q: query,
          limit: 10
        })
      });

      if (!response.ok) {
        throw new Error(`Ошибка поиска: ${response.status}`);
      }

      const data = await response.json();

      if (data?.hits) {
        setSearchResults(data.hits as SearchResult[]);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Ошибка при выполнении поиска:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Обработчик изменения поискового запроса
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Используем debounce для поиска
    const timeoutId = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // Функция для получения URL в зависимости от типа документа
  const getItemUrl = (item: SearchResult): string => {
    let url = '/';

    try {
      if (item._index === 'doctors') {
        url = `/specialists/${item.slug || item.id}`;
      } else if (item._index === 'services') {
        url = `/services/${item.slug || item.id}`;
      } else if (item._index === 'promos') {
        url = `/promos/${item.slug || item.id}`;
      } else if (item._index === 'news') {
        url = `/news/${item.slug || item.id}`;
      } else if (item._index === 'pages') {
        url = `/${item.slug || ''}`;
      } else if (item._index === 'faq') {
        url = `/faq#${item.id}`;
      } else if (item._index === 'prices') {
        // Если есть связанная услуга, переходим на страницу услуги
        if (item.service_id) {
          url = `/services/${item.service_id}`;
        } else {
          // Иначе переходим на страницу цен
          url = '/prices';
        }
      }

      // Проверяем, что URL не пустой и не содержит только слеш
      if (!url || url === '/') {
        console.warn(`Не удалось сформировать URL для элемента: ${JSON.stringify(item)}`);
        return '/';
      }

      return url;
    } catch (error) {
      console.error('Ошибка при формировании URL:', error, item);
      return '/';
    }
  };

  // Функция для получения заголовка результата
  const getItemTitle = (item: SearchResult): string => {
    if (item._index === 'doctors') {
      // Для врачей формируем полное имя
      return item.fullName.trim();
    }

    if (item._index === 'prices') {
      // Для цен формируем заголовок с ценой
      const name = item.name || item.service_name || 'Услуга';
      const price = item.price ? `${item.price} ₽` : '';
      return price ? `${name} - ${price}` : name;
    }

    if (item.name) return item.name;
    if (item.title) return item.title;
    if (item.question) return item.question;
    return 'Без названия';
  };

  // Функция для получения описания результата
  const getItemDescription = (item: SearchResult): string => {
    if (item.short_description) {
      return `${item.short_description.replace(/<[^>]*>/g, '').substring(0, 100)}...`;
    }
    if (item.content) {
      return `${item.content.replace(/<[^>]*>/g, '').substring(0, 100)}...`;
    }
    return '';
  };

  // Функция для получения иконки типа результата
  const getItemTypeLabel = (item: SearchResult): string => {
    switch (item._index) {
      case 'doctors':
        return 'Специалист';
      case 'services':
        return 'Услуга';
      case 'promos':
        return 'Акция';
      case 'news':
        return 'Новость';
      case 'pages':
        return 'Страница';
      case 'faq':
        return 'FAQ';
      case 'prices':
        return 'Цена';
      default:
        return '';
    }
  };

  // Фокус на поле ввода при открытии диалога
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  return (
    <>
      <div className={className}>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          onClick={() => setIsOpen(true)}
          className={`${mobile ? 'md:hidden' : 'hidden md:inline-flex'} items-center justify-center rounded-full bg-gradient-to-br from-olive-100/60 to-olive-200/60 p-2 text-olive-700 backdrop-blur-sm hover:from-olive-200/70 hover:to-olive-300/70 hover:text-olive-600 focus:outline-none shadow-lg transition-all duration-300 hover:scale-105`}
          aria-label="Поиск по сайту"
        >
          <SearchIcon className="h-5 w-5" />
        </Button>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent">Поиск по сайту</DialogTitle>
          </DialogHeader>

          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-olive-400" />
            <Input
              ref={inputRef}
              type="text"
              placeholder="Введите запрос для поиска..."
              className="pl-10 bg-white border-olive-200 focus:border-olive-400 focus:ring-olive-400 h-12 rounded-xl shadow-sm"
              value={searchQuery}
              onChange={handleSearchChange}
            />
            {searchQuery && (
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2 text-olive-400 hover:text-olive-600"
                onClick={() => {
                  setSearchQuery('');
                  setSearchResults([]);
                  inputRef.current?.focus();
                }}
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>

          {/* Контейнер с фиксированной высотой для результатов поиска */}
          <div className="h-[400px] overflow-y-auto mt-4">
            {isLoading && (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-olive-500" />
              </div>
            )}

            {!isLoading && searchResults.length === 0 && searchQuery && (
              <div className="text-center py-8 text-olive-600">
                <p className="text-lg font-medium">Ничего не найдено</p>
                <p className="text-sm mt-2">Попробуйте изменить поисковый запрос</p>
              </div>
            )}

            {!isLoading && searchResults.length > 0 && (
              <div className="space-y-4">
                {searchResults.map((result) => (
                  <a
                    key={`${result._index}-${result.id}`}
                    href={getItemUrl(result)}
                    className="block p-4 rounded-lg border border-olive-100 hover:bg-olive-50 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-olive-800">{getItemTitle(result)}</h3>
                      <span className="text-xs bg-olive-100 text-olive-600 px-2 py-1 rounded-full">
                        {getItemTypeLabel(result)}
                      </span>
                    </div>

                    {/* Дополнительная информация в зависимости от типа результата */}
                    {result._index === 'doctors' && (
                      <div className="mt-1 text-sm font-medium text-olive-600">
                        {result.position || 'Врач'}
                      </div>
                    )}

                    {result._index === 'prices' && (
                      <div className="mt-1 flex items-center">
                        <span className="text-sm font-semibold text-olive-700">{result.price} ₽</span>
                        {result.old_price && result.old_price > 0 && (
                          <span className="ml-2 text-xs line-through text-olive-400">{result.old_price} ₽</span>
                        )}
                        {result.category_name && (
                          <span className="ml-2 text-xs text-olive-500">• {result.category_name}</span>
                        )}
                      </div>
                    )}

                    {result._index === 'services' && result.category && (
                      <div className="mt-1 text-xs text-olive-500">
                        Категория: {result.category}
                      </div>
                    )}

                    {/* {getItemDescription(result) && (
                      <p className="mt-2 text-sm text-olive-600 line-clamp-2">
                        {getItemDescription(result)}
                      </p>
                    )} */}

                    {/* Отображение URL для удобства */}
                    <div className="mt-2 text-xs text-olive-400 truncate">
                      {getItemUrl(result).startsWith('/')
                        ? `${window.location.origin}${getItemUrl(result)}`
                        : getItemUrl(result)}
                    </div>
                  </a>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
