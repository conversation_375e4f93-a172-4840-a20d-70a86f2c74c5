import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, ArrowRightIcon } from 'lucide-react';
import { type News } from '@/lib/api';
import {motion} from 'framer-motion'

interface NewsPageProps {
  news: News[];
  isAuthenticated?: boolean;
}

export const NewsPage = ({ news, isAuthenticated = false }: NewsPageProps) => {
  const [visibleNews, setVisibleNews] = useState(6);

  const loadMore = () => {
    setVisibleNews(prev => prev + 6);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getImageUrl = (newsItem: News) => {
    if (!newsItem.image) return '/placeholder-news.jpg';
    
    if (typeof newsItem.image === 'string') {
      return `https://pb.stom-line.ru/api/files/news/${newsItem.id}/${newsItem.image}`;
    }
    
    if (newsItem.image && typeof newsItem.image === 'object' && newsItem.image.filename) {
      return `https://pb.stom-line.ru/api/files/news/${newsItem.id}/${newsItem.image.filename}`;
    }
    
    return '/placeholder-news.jpg';
  };

  const getExcerpt = (content?: string) => {
    if (!content) return '';
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.length > 150 ? textContent.substring(0, 150) + '...' : textContent;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-olive-50 via-white to-olive-50/50">
      {/* Enhanced Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <motion.div
          animate={{
            x: [0, 30, 0],
            y: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute -left-[10%] top-[20%] h-[400px] w-[400px] rounded-full bg-gradient-to-r from-olive-300/20 to-olive-400/30 blur-[120px]"
        />
        <motion.div
          animate={{
            x: [0, -25, 0],
            y: [0, 15, 0],
            scale: [1, 0.9, 1]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 5
          }}
          className="absolute -right-[5%] top-[40%] h-[300px] w-[300px] rounded-full bg-gradient-to-l from-olive-400/25 to-olive-500/20 blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, 20, 0],
            y: [0, -30, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 10
          }}
          className="absolute bottom-[20%] left-[20%] h-[350px] w-[350px] rounded-full bg-gradient-to-tr from-olive-300/15 to-olive-200/25 blur-[140px]"
        />
      </div>

      {/* Animated grid pattern */}
      <div className="pointer-events-none absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.03] animate-pulse" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-20">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Badge className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white mb-8 px-6 py-2 text-sm font-medium shadow-lg">
              <CalendarIcon className="h-4 w-4 mr-2" />
              Новости клиники
            </Badge>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-gray-900 via-olive-800 to-gray-900 bg-clip-text text-transparent mb-8"
          >
            Новости клиники
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed"
          >
            Следите за последними новостями, достижениями и событиями нашей стоматологической клиники
          </motion.p>
        </motion.div>

        {/* Featured News */}
        {news.length > 0 && news[0].is_featured && (
          <div className="mb-16">
            <Card className="overflow-hidden border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
              <div className="grid md:grid-cols-2 gap-0">
                <div className="relative h-64 md:h-full">
                  <img
                    src={getImageUrl(news[0])}
                    alt={news[0].title}
                    className="w-full h-full object-cover"
                  />
                  <Badge className="absolute top-4 left-4 bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 shadow-lg">
                    Главная новость
                  </Badge>
                </div>
                <div className="p-8 flex flex-col justify-center">
                  <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <CalendarIcon className="h-4 w-4" />
                    {formatDate(news[0].date)}
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                    {news[0].title}
                  </h2>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {getExcerpt(news[0].content)}
                  </p>
                  <Button 
                    asChild 
                    className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white w-fit shadow-lg"
                  >
                    <a href={`/news/${news[0].slug}`}>
                      Читать полностью
                      <ArrowRightIcon className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* News Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {news.slice(news[0]?.is_featured ? 1 : 0, visibleNews).map((newsItem) => (
            <Card 
              key={newsItem.id} 
              className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:scale-105"
            >
              <div className="relative h-48">
                <img
                  src={getImageUrl(newsItem)}
                  alt={newsItem.title}
                  className="w-full h-full object-cover"
                />
                {newsItem.is_featured && (
                  <Badge className="absolute top-4 left-4 bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 shadow-lg">
                    Рекомендуем
                  </Badge>
                )}
              </div>
              <CardHeader>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                  <CalendarIcon className="h-4 w-4" />
                  {formatDate(newsItem.date)}
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 line-clamp-2">
                  {newsItem.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 mb-4 line-clamp-3">
                  {getExcerpt(newsItem.content)}
                </CardDescription>
                <Button 
                  asChild 
                  variant="outline" 
                  className="w-full border-olive-200 text-olive-700 hover:bg-olive-50 shadow-lg"
                >
                  <a href={`/news/${newsItem.slug}`}>
                    Читать далее
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </a>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More Button */}
        {visibleNews < news.length && (
          <div className="text-center">
            <Button 
              onClick={loadMore}
              className="bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white px-8 py-3 shadow-xl"
            >
              Показать ещё новости
            </Button>
          </div>
        )}

        {/* Empty State */}
        {news.length === 0 && (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-olive-100 to-olive-200 rounded-full flex items-center justify-center shadow-lg">
                <CalendarIcon className="h-12 w-12 text-olive-600" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Новостей пока нет
              </h3>
              <p className="text-gray-600">
                Мы работаем над наполнением этого раздела. Следите за обновлениями!
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
