"use client"

import { useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { TextGenerateEffect } from "./text-generate-effect"
import { AuroraText } from "./magicui/aurora-text"

export default function HeroSection() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    const particles: Particle[] = []
    const particleCount = 50

    class Particle {
      x: number
      y: number
      size: number
      speedX: number
      speedY: number
      color: string

      constructor() {
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.size = Math.random() * 3 + 1
        this.speedX = Math.random() * 0.5 - 0.25
        this.speedY = Math.random() * 0.5 - 0.25
        this.color = `rgba(127, 134, 67, ${Math.random() * 0.3 + 0.1})`
      }

      update() {
        this.x += this.speedX
        this.y += this.speedY

        if (this.x > canvas.width) this.x = 0
        else if (this.x < 0) this.x = canvas.width

        if (this.y > canvas.height) this.y = 0
        else if (this.y < 0) this.y = canvas.height
      }

      draw() {
        ctx.fillStyle = this.color
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle())
    }

    function connectParticles() {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x
          const dy = particles[i].y - particles[j].y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 100) {
            ctx.beginPath()
            ctx.strokeStyle = `rgba(127, 134, 67, ${0.1 - distance / 1000})`
            ctx.lineWidth = 0.5
            ctx.moveTo(particles[i].x, particles[i].y)
            ctx.lineTo(particles[j].x, particles[j].y)
            ctx.stroke()
          }
        }
      }
    }

    function animate() {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      for (const particle of particles) {
        particle.update()
        particle.draw()
      }

      connectParticles()
      requestAnimationFrame(animate)
    }

    animate()

    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return (
    <section className="relative min-h-[80vh] sm:min-h-[90vh] overflow-hidden py-12 sm:py-20 md:py-32">
      <canvas ref={canvasRef} className="absolute inset-0 z-0 h-full w-full"></canvas>
      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <div className="grid gap-6 md:grid-cols-2 md:gap-10">
          <div className="flex flex-col justify-center space-y-4 sm:space-y-6 text-center md:text-left">
            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-olive-100/80 to-olive-200/80 px-4 py-2 text-xs sm:text-sm font-medium text-olive-700 backdrop-blur-sm shadow-lg border border-olive-200/50 mx-auto md:mx-0">
              <span className="mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-olive-500 to-olive-600"></span>
              Сайт в разработке
            </div>
            <div className="space-y-3 sm:space-y-4">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tighter text-olive-900">
                Стоматология{" "}
                <div className="mt-2 sm:mt-3 bg-gradient-to-r from-olive-600 to-olive-500 bg-clip-text text-transparent">
                  {/* <TextGenerateEffect className="text-olive-600 text-5xl" words="Будущего" /> */}
                  <AuroraText speed={2}>Будущего</AuroraText>
                </div>
              </h1>
              <p className="max-w-[600px] text-base sm:text-lg text-olive-700 md:text-xl mx-auto md:mx-0">
                Скоро здесь будет представлена информация о наших инновационных стоматологических услугах. Следите за
                обновлениями!
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
              <Button className="group relative overflow-hidden bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 text-sm sm:text-base">
                <span className="relative z-10">Связаться с нами</span>
                <span className="absolute inset-0 -z-0 translate-y-full bg-gradient-to-r from-olive-600 to-olive-700 transition-transform duration-300 group-hover:translate-y-0"></span>
              </Button>
              <Button className="bg-gradient-to-r from-olive-100/80 to-olive-200/80 text-olive-700 hover:from-olive-200/90 hover:to-olive-300/90 hover:text-olive-600 backdrop-blur-sm shadow-lg border border-olive-200/50 text-sm sm:text-base">
                Узнать больше
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-center mt-6 md:mt-0">
            <div className="relative aspect-square w-full max-w-[280px] sm:max-w-[350px] md:max-w-[450px]">
              <div className="absolute inset-0 flex items-center justify-center rounded-full bg-gradient-to-br from-olive-200/70 to-olive-300/70 backdrop-blur-sm">
                <div className="relative h-[80%] w-[80%] overflow-hidden rounded-full border border-olive-400/30 bg-gradient-to-br from-olive-100/70 to-olive-200/70 p-1">
                  <div className="absolute inset-0 flex items-center justify-center text-olive-600">
                    <div className="relative">
                      <div className="absolute -inset-4 animate-spin-slow rounded-full border border-dashed border-olive-400/40"></div>
                      <div className="absolute -inset-8 animate-spin-slow-reverse rounded-full border border-dashed border-olive-400/30"></div>
                      <div className="relative z-10 text-center">
                        {/* <div className="text-4xl font-bold text-olive-700">Сайт в разработке</div> */}
                        <div className="my-2 sm:my-3 text-xs sm:text-sm text-olive-600">
                          <TextGenerateEffect className="text-lg sm:text-2xl" words="Сайт в разработке" />
                        </div>
                        <div className="my-2 sm:my-3 text-base sm:text-lg text-olive-600">
                          (8152) 52-57-08
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="absolute -bottom-4 -left-4 h-16 w-16 sm:h-24 sm:w-24 rounded-full bg-olive-400/30 blur-3xl"></div>
              <div className="absolute -right-4 -top-4 h-16 w-16 sm:h-24 sm:w-24 rounded-full bg-olive-400/30 blur-3xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

