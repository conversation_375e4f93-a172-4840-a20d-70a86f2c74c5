"use client"

import { useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function AboutSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const imageRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current || !imageRef.current) return

      const sectionTop = sectionRef.current.getBoundingClientRect().top
      const windowHeight = window.innerHeight

      if (sectionTop < windowHeight * 0.75) {
        imageRef.current.classList.add("translate-x-0", "opacity-100")
        imageRef.current.classList.remove("translate-x-full", "opacity-0")
      }
    }

    window.addEventListener("scroll", handleScroll)
    handleScroll() // Check on initial load

    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <section ref={sectionRef} className="relative overflow-hidden py-20 md:py-32">
      <div className="absolute inset-0 bg-gradient-to-br from-olive-50 via-white to-olive-50/50"></div>
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]"></div>

      <div className="container relative z-10 mx-auto px-4 md:px-6">
        <div className="grid gap-10 md:grid-cols-2">
          <div
            ref={imageRef}
            className="flex items-center justify-center transition-all duration-1000 ease-out md:translate-x-full md:opacity-0"
          >
            <div className="relative aspect-square w-full max-w-[500px] overflow-hidden rounded-2xl">
              <div className="absolute inset-0 bg-gradient-to-br from-olive-100/80 to-olive-200/80 backdrop-blur-sm shadow-inner">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-olive-700">Фото клиники</div>
                    <div className="mt-2 text-sm text-olive-600">будет добавлено</div>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -bottom-4 -left-4 h-24 w-24 rounded-full bg-olive-300/30 blur-3xl"></div>
              <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-olive-300/30 blur-3xl"></div>

              {/* Decorative circles */}
              <div className="absolute left-4 top-4 h-20 w-20 rounded-full border border-olive-400/30"></div>
              <div className="absolute bottom-4 right-4 h-16 w-16 rounded-full border border-olive-400/30"></div>
              <div className="absolute bottom-20 left-20 h-8 w-8 rounded-full border border-olive-400/40"></div>
            </div>
          </div>

          <div className="flex flex-col justify-center space-y-6">
            <div className="inline-flex items-center rounded-full bg-gradient-to-r from-olive-100/80 to-olive-200/80 px-4 py-2 text-sm font-medium text-olive-700 backdrop-blur-sm shadow-lg border border-olive-200/50">
              <span className="mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-olive-500 to-olive-600"></span>О нас
            </div>
            <div className="space-y-4">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tighter text-gray-900">
                Стоматология нового{" "}
                <span className="bg-gradient-to-r from-olive-600 to-olive-500 bg-clip-text text-transparent">
                  поколения
                </span>
              </h2>
              <p className="text-olive-700 md:text-lg">
                Наша стоматологическая клиника предоставляет полный спектр услуг с использованием передовых технологий.
                Мы стремимся создать комфортную атмосферу и обеспечить высокое качество лечения для каждого пациента.
              </p>
              <p className="text-olive-700 md:text-lg">
                Наши специалисты имеют многолетний опыт работы и постоянно совершенствуют свои навыки, чтобы
                предоставлять вам лучшие стоматологические услуги с использованием инновационных методик и материалов.
              </p>
            </div>
            <div>
              <Button className="group relative overflow-hidden bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                <span className="relative z-10">Подробнее о нас</span>
                <span className="absolute inset-0 -z-0 translate-y-full bg-gradient-to-r from-olive-600 to-olive-700 transition-transform duration-300 group-hover:translate-y-0"></span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

