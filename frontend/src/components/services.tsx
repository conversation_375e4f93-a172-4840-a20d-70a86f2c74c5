'use client'

import { useEffect, useState } from 'react'
import { ArrowRight, Check, Star, Calendar, Phone, User } from 'lucide-react'
import { getServices, getServiceById, type Service } from '@/lib/api'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { EditButton } from '@/components/admin/EditButton'

// Animation variants
const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const AnimateOnScroll = ({ children, className, delay = 0 }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <motion.div
      ref={ref}
      initial='hidden'
      animate={inView ? 'visible' : 'hidden'}
      variants={{
        hidden: { opacity: 0, y: 30 },
        visible: {
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.6,
            delay
          }
        }
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

interface ServicesPageProps {
  specialist?: string;
  services: Service[];
}

export const ServicesPage = ({ specialist, services }: ServicesPageProps) => {
  const [loading, setLoading] = useState(false);
  const [specialistName, setSpecialistName] = useState<string>('');

  // Получаем имя специалиста, если есть ID специалиста
  useEffect(() => {
    const fetchSpecialistName = async () => {
      if (specialist) {
        try {
          const response = await fetch(`${import.meta.env.PUBLIC_API_URL}/api/collections/doctors/records/${specialist}`);
          if (response.ok) {
            const data = await response.json();
            setSpecialistName(`${data.surname} ${data.name} ${data.patronymic || ''}`.trim());
          }
        } catch (error) {
          console.error('Ошибка при получении имени специалиста:', error);
        }
      }
    };

    fetchSpecialistName();
  }, [specialist])

  // Отладка: выводим информацию о полученных услугах
  useEffect(() => {
    console.log('Получено услуг в компоненте:', services.length);
    if (services.length > 0) {
      console.log('Пример услуги:', {
        id: services[0].id,
        title: services[0].title,
        name: services[0].name,
        expand: services[0].expand
      });
    }
  }, [services])

  return (
    <div className='relative flex min-h-screen flex-col bg-gradient-to-br from-olive-50 via-white to-olive-50/50'>
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]" />

      {/* Hero Section */}
      <section className='relative flex h-[80vh] w-full items-center z-10'>
        <div className='absolute inset-0 z-0'>
          <div className='absolute inset-0 z-10 bg-gradient-to-r from-olive-100/30 to-olive-200/70' />
        </div>
        <div className='relative z-20 container mx-auto px-4 md:px-6'>
          <motion.div className='max-w-3xl' initial={{ opacity: 0, y: 30 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
            {/* <Badge className='bg-olive-400 hover:bg-olive-500 mb-4 text-white'>Премиум-сегмент</Badge> */}
            <motion.h1
              className='mb-6 text-4xl font-bold tracking-tight text-zinc-700 md:text-5xl lg:text-6xl'
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              {specialist && specialistName ? `Услуги специалиста: ${specialistName}` : 'Стоматологические услуги высочайшего качества'}
            </motion.h1>
            <motion.p
                          className='mb-8 max-w-2xl text-lg text-zinc-700/90 md:text-xl'
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {specialist && specialistName ?
                `Все услуги, которые предоставляет ${specialistName}. Выберите интересующую вас услугу и запишитесь на прием.` :
                'Мы используем передовые технологии и материалы, чтобы обеспечить вам идеальную улыбку и здоровье полости рта'
              }
            </motion.p>
            <motion.div
              className='flex flex-wrap gap-4'
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Button size='lg' className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl'>
                Записаться на консультацию
                <ArrowRight className='ml-2 h-4 w-4' />
              </Button>

              {specialist && specialistName ? (
                <a href='/specialists' className='inline-flex'>
                  <Button size='lg' variant='outline' className='border-olive-200/50 bg-olive-100/70 text-olive-700 backdrop-blur-sm hover:bg-olive-200/50 shadow-lg'>
                    <User className='mr-2 h-4 w-4' />
                    Вернуться к специалистам
                  </Button>
                </a>
              ) : (
                <Button size='lg' variant='outline' className='border-olive-200/50 bg-olive-100/70 text-olive-700 backdrop-blur-sm hover:bg-olive-200/50 shadow-lg'>
                  Узнать больше
                </Button>
              )}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Introduction */}
      <section className='relative bg-white/80 backdrop-blur-sm py-20 z-10'>
        <div className='container mx-auto px-4 md:px-6'>
          <div className='flex flex-col items-center gap-12 md:flex-row'>
            <AnimateOnScroll className='space-y-6 md:w-1/2'>
              <Badge className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-lg'>Наш подход</Badge>
              <h2 className='text-3xl font-bold tracking-tight md:text-4xl'>Современная стоматология с заботой о вашем комфорте</h2>
              <p className='text-lg text-gray-600'>
                Мы объединили передовые технологии, опыт лучших специалистов и премиальный сервис, чтобы сделать ваше лечение максимально комфортным и
                эффективным. Наша клиника специализируется на предоставлении высококачественных стоматологических услуг с индивидуальным подходом к каждому
                пациенту.
              </p>
              <motion.div
                className='grid grid-cols-1 gap-4 sm:grid-cols-2'
                variants={staggerContainer}
                initial='hidden'
                whileInView='visible'
                viewport={{ once: true, amount: 0.3 }}
              >
                <motion.div className='flex items-start' variants={fadeIn}>
                  <div className='bg-olive-100 mr-3 rounded-full p-2'>
                    <Check className='text-olive-600 h-4 w-4' />
                  </div>
                  <div>
                    <h3 className='font-medium'>Безболезненное лечение</h3>
                    <p className='text-sm text-gray-500'>Современные методы анестезии</p>
                  </div>
                </motion.div>
                <motion.div className='flex items-start' variants={fadeIn}>
                  <div className='bg-olive-100 mr-3 rounded-full p-2'>
                    <Check className='text-olive-600 h-4 w-4' />
                  </div>
                  <div>
                    <h3 className='font-medium'>Цифровые технологии</h3>
                    <p className='text-sm text-gray-500'>3D-моделирование и планирование</p>
                  </div>
                </motion.div>
                <motion.div className='flex items-start' variants={fadeIn}>
                  <div className='bg-olive-100 mr-3 rounded-full p-2'>
                    <Check className='text-olive-600 h-4 w-4' />
                  </div>
                  <div>
                    <h3 className='font-medium'>Премиальные материалы</h3>
                    <p className='text-sm text-gray-500'>Лучшие мировые производители</p>
                  </div>
                </motion.div>
                <motion.div className='flex items-start' variants={fadeIn}>
                  <div className='bg-olive-100 mr-3 rounded-full p-2'>
                    <Check className='text-olive-600 h-4 w-4' />
                  </div>
                  <div>
                    <h3 className='font-medium'>Гарантия качества</h3>
                    <p className='text-sm text-gray-500'>На все виды услуг</p>
                  </div>
                </motion.div>
              </motion.div>
            </AnimateOnScroll>
            <AnimateOnScroll className='relative h-[400px] w-full overflow-hidden rounded-2xl shadow-2xl md:w-1/2' delay={0.3}>
              <img src='/placeholder.svg?height=800&width=1200' alt='Современная стоматологическая клиника' fill className='object-cover' />
            </AnimateOnScroll>
          </div>
        </div>
      </section>

      {/* Featured Services */}
      <section className='relative bg-olive-50/80 backdrop-blur-sm py-20 z-10'>
        <div className='container mx-auto px-4 md:px-6'>
          <AnimateOnScroll className='mx-auto mb-16 max-w-3xl text-center'>
            <Badge className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 mb-4 text-white shadow-lg'>Наши услуги</Badge>
            <h2 className='mb-4 text-3xl font-bold tracking-tight md:text-4xl'>Комплексный подход к здоровью вашей улыбки</h2>
            <p className='text-lg text-gray-600'>Мы предлагаем полный спектр стоматологических услуг — от профилактики до сложного хирургического лечения</p>
          </AnimateOnScroll>

          <motion.div
            className='grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3'
            variants={staggerContainer}
            initial='hidden'
            whileInView='visible'
            viewport={{ once: true, amount: 0.1 }}
          >
            {services.slice(0, 3).map((service, index) => (
              <motion.div key={service.id} className='group' variants={fadeIn}>
                <div className='relative mb-6 h-[300px] w-full overflow-hidden rounded-2xl'>
                  <div className='absolute inset-0 z-10 bg-gradient-to-t from-black/70 to-transparent' />
                  <img
                    src={service.image ? `${import.meta.env.PUBLIC_API_URL || 'https://pb.stom-line.ru'}/api/files/services/${service.id}/${service.image}` : '/placeholder.svg?height=600&width=800'}
                    alt={service.title}
                    className='w-full h-full object-cover transition-transform duration-500 group-hover:scale-105'
                  />
                  <div className='absolute right-0 bottom-0 left-0 z-20 p-6'>
                    <h3 className='mb-2 text-xl font-bold text-white'>{service.title}</h3>
                    <p className='text-white/80'>{service.short_description || ''}</p>
                  </div>
                </div>
                <div className='space-y-4'>
                  <div className='flex items-center text-sm text-gray-500'>
                    <div className='mr-4 flex items-center'>
                      <Star className='text-olive-400 mr-1 h-4 w-4' />
                      <span>{service.expand?.category?.name || 'Услуга'}</span>
                    </div>
                    {service.price && (
                      <span>{service.price_from ? 'от ' : ''}{service.price} ₽</span>
                    )}
                  </div>
                  <div className='text-gray-600' dangerouslySetInnerHTML={{ __html: service.short_description || '' }} />

                  {service.content && (
                    <Button variant='link' className='text-olive-600 h-auto p-0 font-medium'>
                      Подробнее <ArrowRight className='ml-1 h-4 w-4' />
                    </Button>
                  )}
                </div>
              </motion.div>
            ))}

            {services.length === 0 && (
              <div className='col-span-3 text-center py-10'>
                <p className='text-gray-500'>Услуги загружаются или отсутствуют в базе данных.</p>
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* All Services */}
      <section className='relative bg-white/80 backdrop-blur-sm py-20 z-10'>
        <div className='container mx-auto px-4 md:px-6'>
          <AnimateOnScroll className='mx-auto mb-16 max-w-3xl text-center'>
            <Badge className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 mb-4 text-white shadow-lg'>Полный спектр</Badge>
            <h2 className='mb-4 text-3xl font-bold tracking-tight md:text-4xl'>Все стоматологические услуги в одной клинике</h2>
            <p className='text-lg text-gray-600'>
              От профилактики до сложного хирургического лечения — мы предлагаем комплексный подход к здоровью вашей улыбки
            </p>
          </AnimateOnScroll>

          {/* Группируем услуги по категориям */}
          {services.length > 0 ? (
            <motion.div
              className='grid grid-cols-1 gap-x-8 gap-y-12 md:grid-cols-2 lg:grid-cols-3'
              variants={staggerContainer}
              initial='hidden'
              whileInView='visible'
              viewport={{ once: true, amount: 0.1 }}
            >
              {/* Группируем услуги по категориям */}
              {Array.from(new Set(services.map(service => service.expand?.category?.name || 'Другое'))).map((categoryName, index) => {
                // Фильтруем услуги по текущей категории
                const categoryServices = services.filter(service =>
                  (service.expand?.category?.name || 'Другое') === categoryName
                );

                return (
                  <motion.div key={`category-${index}`} variants={fadeIn}>
                    <div className='mb-4 flex items-center'>
                      <div className='bg-olive-100 mr-4 flex h-12 w-12 items-center justify-center rounded-full'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          width='24'
                          height='24'
                          viewBox='0 0 24 24'
                          fill='none'
                          stroke='currentColor'
                          strokeWidth='2'
                          strokeLinecap='round'
                          strokeLinejoin='round'
                          className='text-olive-600'
                          aria-hidden='true'
                        >
                          <path d='M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z' />
                          <path d='m9 12 2 2 4-4' />
                        </svg>
                      </div>
                      <h3 className='text-xl font-bold'>{categoryName}</h3>
                    </div>
                    <ul className='space-y-3 text-gray-600'>
                      {categoryServices.slice(0, 6).map((service, serviceIndex) => (
                      <li key={`service-${service.id}`} className='flex items-center justify-between group'>
                      <div className='flex items-center'>
                        <Check className='text-olive-600 mr-2 h-4 w-4 flex-shrink-0' />
                          <span>{service.title || service.name}</span>
                          </div>
                           <EditButton 
                             collection="services" 
                             id={service.id} 
                             position="inline" 
                             size="sm"
                             className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                           />
                         </li>
                       ))}
                      {categoryServices.length > 6 && (
                        <li className='flex items-center'>
                          <span className='text-olive-600 ml-6 text-sm'>И еще {categoryServices.length - 6} услуг...</span>
                        </li>
                      )}
                    </ul>
                  </motion.div>
                );
              })}
            </motion.div>
          ) : (
            <div className='text-center py-10'>
              <p className='text-gray-500'>Услуги загружаются или отсутствуют в базе данных.</p>
            </div>
          )}
        </div>
      </section>

      {/* Pricing */}
      <section className='relative bg-olive-50/80 backdrop-blur-sm py-20 z-10'>
        <div className='container mx-auto px-4 md:px-6'>
          <AnimateOnScroll className='mx-auto mb-16 max-w-3xl text-center'>
            <Badge className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 mb-4 text-white shadow-lg'>Прайс-лист</Badge>
            <h2 className='mb-4 text-3xl font-bold tracking-tight md:text-4xl'>Прозрачное ценообразование</h2>
            <p className='text-lg text-gray-600'>Мы предлагаем оптимальное соотношение цены и качества для всех наших услуг</p>
          </AnimateOnScroll>

          {services.length > 0 ? (
            <motion.div
              className='grid grid-cols-1 gap-8 lg:grid-cols-3'
              variants={staggerContainer}
              initial='hidden'
              whileInView='visible'
              viewport={{ once: true, amount: 0.1 }}
            >
              {/* Группируем услуги по категориям для прайс-листа */}
              {Array.from(new Set(services.map(service => service.expand?.category?.name || 'Другое'))).
                slice(0, 3).map((categoryName, index) => {
                // Фильтруем услуги по текущей категории и выбираем только те, у которых есть цена
                const categoryServices = services.filter(service =>
                  (service.expand?.category?.name || 'Другое') === categoryName && service.price
                );

                // Выбираем цвет фона в зависимости от индекса
                const bgColors = ['bg-olive-400', 'bg-olive-600', 'bg-olive-500'];
                const bgColor = bgColors[index % bgColors.length];

                return (
                  <motion.div key={`price-category-${index}`} variants={fadeIn}>
                    <Card className='overflow-hidden rounded-2xl border-0 bg-white shadow-lg'>
                      <CardContent className='p-0'>
                        <div className={`${bgColor} p-6 text-white`}>
                          <h3 className='mb-2 text-xl font-bold'>{categoryName}</h3>
                          <p className='opacity-90'>{categoryServices.length} услуг</p>
                        </div>
                        <div className='space-y-4 p-6'>
                          {categoryServices.slice(0, 4).map((service, serviceIndex) => (
                            <div key={`price-service-${service.id}`} className={`flex items-center justify-between ${serviceIndex < 3 ? 'border-b pb-4' : ''}`}>
                              <span>{service.title || service.name}</span>
                              <span className='font-semibold'>{service.price_from ? 'от ' : ''}{service.price} ₽</span>
                            </div>
                          ))}

                          {categoryServices.length > 4 && (
                            <div className='flex justify-end pt-2'>
                              <span className='text-olive-600 text-sm'>И еще {categoryServices.length - 4} услуг...</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}

              {/* Если категорий меньше 3, добавляем дополнительную карточку */}
              {Array.from(new Set(services.map(service => service.expand?.category?.name || 'Другое'))).length < 3 && (
                <motion.div variants={fadeIn}>
                  <Card className='overflow-hidden rounded-2xl border-0 bg-white shadow-lg'>
                    <CardContent className='p-0'>
                      <div className='bg-olive-500 p-6 text-white'>
                        <h3 className='mb-2 text-xl font-bold'>Другие услуги</h3>
                        <p className='opacity-90'>Дополнительные услуги</p>
                      </div>
                      <div className='space-y-4 p-6'>
                        <div className='flex items-center justify-between border-b pb-4'>
                          <span>Консультация специалиста</span>
                          <span className='font-semibold'>Бесплатно</span>
                        </div>
                        <div className='flex items-center justify-between border-b pb-4'>
                          <span>Составление плана лечения</span>
                          <span className='font-semibold'>Бесплатно</span>
                        </div>
                        <div className='flex items-center justify-between'>
                          <span>Повторный прием</span>
                          <span className='font-semibold'>Бесплатно</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </motion.div>
          ) : (
            <div className='text-center py-10'>
              <p className='text-gray-500'>Цены загружаются или отсутствуют в базе данных.</p>
            </div>
          )}

          <AnimateOnScroll className='mt-12 text-center' delay={0.3}>
            <Button size='lg' className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl'>
              Скачать полный прайс-лист
            </Button>
            <p className='mt-4 text-sm text-gray-500'>
              * Указанные цены являются ориентировочными. Точная стоимость определяется после консультации и диагностики.
            </p>
          </AnimateOnScroll>
        </div>
      </section>

      {/* CTA Section */}
      <section className='relative bg-white/80 backdrop-blur-sm py-20 z-10'>
        <div className='container mx-auto px-4 md:px-6'>
          <div className='flex flex-col items-center gap-12 lg:flex-row'>
            <AnimateOnScroll className='space-y-6 lg:w-1/2'>
              <Badge className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-lg'>Запись на прием</Badge>
              <h2 className='text-3xl font-bold tracking-tight md:text-4xl'>Запишитесь на консультацию к нашим специалистам</h2>
              <p className='text-lg text-gray-600'>
                Наши врачи проведут полную диагностику и составят индивидуальный план лечения, учитывающий все ваши пожелания и особенности
              </p>
              <motion.div
                className='flex flex-col gap-4 sm:flex-row'
                variants={staggerContainer}
                initial='hidden'
                whileInView='visible'
                viewport={{ once: true }}
              >
                <motion.div variants={fadeIn}>
                  <Button size='lg' className='bg-gradient-to-r from-olive-500 to-olive-600 hover:from-olive-600 hover:to-olive-700 text-white shadow-xl'>
                    <Calendar className='mr-2 h-5 w-5' />
                    Записаться онлайн
                  </Button>
                </motion.div>
                <motion.div variants={fadeIn}>
                  <Button size='lg' variant='outline' className='border-olive-200 text-olive-700 hover:bg-olive-50 shadow-lg'>
                    <Phone className='mr-2 h-5 w-5' />
                    Позвонить
                  </Button>
                </motion.div>
              </motion.div>
            </AnimateOnScroll>
            <AnimateOnScroll className='relative h-[400px] w-full overflow-hidden rounded-2xl shadow-2xl lg:w-1/2' delay={0.3}>
              <img src='/placeholder.svg?height=800&width=1200' alt='Запись на консультацию' fill className='object-cover' />
            </AnimateOnScroll>
          </div>
        </div>
      </section>
    </div>
  )
}
