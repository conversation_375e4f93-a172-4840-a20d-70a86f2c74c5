# Agent Guide for Stomline Project

## Commands
- **Build**: `cd frontend && npm run build`
- **Dev**: `cd frontend && npm run dev` (serves on port 4321)
- **Format**: `cd frontend && npx prettier --write .`
- **Search Sync**: `cd frontend && npm run sync-search`

## Project Structure
- **Frontend**: Astro + React app in `/frontend`
- **Backend**: PocketBase instance in `/backend`
- **Database**: PocketBase (pb.stom-line.ru)

## Code Style
- **Prettier**: 160 width, single quotes, no semicolons, 2-space tabs
- **Imports**: Use `@/` alias for src imports
- **Components**: React.FC with prop interfaces, forwardRef for UI components
- **Types**: PascalCase interfaces, extensive use of optional properties `?`
- **Error Handling**: Russian error messages, try-catch blocks with string interpolation
- **Classes**: Tailwind CSS with `cn()` utility for conditional classes
- **Comments**: Russian for user-facing text, English for technical comments

## Conventions
- JSX single quotes, preserve quote props
- Use `client:only="react"` for React components in Astro
- PocketBase integration via `/lib/pocketbase.ts`
- UI components in `/components/ui/` using Radix UI + class-variance-authority
