# Описание архитектуры и особенностей проекта (llm.txt)

## Общая структура
Проект — сайт на Astro + React (frontend) с backend на PocketBase. Контент сайта (специалисты, услуги, акции, прайс, отзывы и др.) хранится в коллекциях PocketBase и редактируется через встроенную админку прямо на сайте.

## Встроенная админка PocketBase

### Основные возможности
- **Авторизация**: через email/пароль, с сохранением токена в localStorage.
- **Динамическая форма редактирования**: для любой коллекции PocketBase, форма строится по схеме коллекции (fields из example_collections.json).
- **Поддержка всех основных типов полей**: text, number, bool, relation (одиночные и множественные), file (загрузка и превью), editor (HTML-редактор).
- **HTML-редактор**: используется Tiptap (@tiptap/react), полностью совместим с React 18+.
- **Алиасы**: для всех полей поддерживаются русские label (если указаны в схеме).
- **Связи**: relation-поля автоматически подгружают варианты из связанных коллекций.
- **Файлы**: поддержка загрузки и предпросмотра файлов/изображений.
- **Кнопки "Редактировать"**: на всех страницах сайта, где выводятся записи из PB, есть кнопка/ссылка "Редактировать", ведущая на /admin/edit/collection/id.
- **Права**: (опционально) можно скрывать кнопки "Редактировать" для неавторизованных пользователей.

### Ключевые файлы
- `src/pages/admin/edit/[collection]/[id].astro` — страница редактирования записи по коллекции и id.
- `src/components/admin/AdminEditWrapper.tsx` — обёртка, переключающая между логином и формой.
- `src/components/admin/PocketbaseLogin.tsx` — форма авторизации.
- `src/components/admin/DynamicEditForm.tsx` — динамическая форма редактирования записи (универсальна для любой коллекции PB).
- `src/components/admin/TiptapEditorWrapper.tsx` — обёртка для HTML-редактора Tiptap.

### Как работает динамическая форма
- Схема коллекции (fields) берётся из example_collections.json.
- Для каждого поля строится соответствующий input:
  - text → Input
  - number → Input type="number"
  - bool → Checkbox
  - relation (maxSelect > 1) → multi-select
  - relation (maxSelect <= 1) → select
  - file → Input type="file" + предпросмотр
  - editor → TiptapEditorWrapper (HTML-редактор)
- Все изменения отправляются PATCH-запросом с авторизацией (токен).

### Как добавить новую коллекцию или поле
1. Добавить/изменить коллекцию в PocketBase и экспортировать/обновить example_collections.json.
2. Кнопка "Редактировать" появится автоматически на страницах, если выводится запись этой коллекции.
3. Форма редактирования автоматически подстроится под новую схему.

---

## Прочее
- Для расширения типов полей или логики формы достаточно доработать DynamicEditForm.tsx.
- Для кастомизации HTML-редактора — редактировать TiptapEditorWrapper.tsx.
- Для интеграции с другими источниками данных или логикой — использовать pbUrl и token.

---

Документ предназначен для LLM/чат-ботов и разработчиков для быстрого понимания архитектуры и принципов работы админки и сайта.
